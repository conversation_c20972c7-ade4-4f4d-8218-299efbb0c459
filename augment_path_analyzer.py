#!/usr/bin/env python3
"""
VSCode Augment插件路径分析器
对路径分析.txt中的内容进行过滤、分类和总结
"""

import os
import re
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Set

class AugmentPathAnalyzer:
    def __init__(self, input_file: str = "路径分析.txt"):
        self.input_file = input_file
        self.paths = []
        self.categories = {
            "插件安装文件": [],
            "配置文件": [],
            "缓存文件": [],
            "日志文件": [],
            "用户数据文件": [],
            "临时文件": [],
            "其他相关文件": []
        }
        
        # 定义过滤规则 - 排除不相关的文件
        self.exclude_patterns = [
            r'networkx.*edge_augmentation',  # NetworkX库文件
            r'networkx.*shortestaugmentingpath',  # NetworkX库文件
            r'torchvision.*augment',  # PyTorch视觉库文件
            r'zod.*augmentation',  # Zod库文件
            r'blackboxapp\.blackbox.*\.augmentignore',  # BlackBox插件文件
            r'\.langflow-venv',  # Langflow虚拟环境
            r'npm-cache',  # NPM缓存
            r'uv\\cache',  # UV缓存
            r'Python312\\Lib\\site-packages',  # Python标准库
            r'__pycache__',  # Python缓存
            r'\.pyc$',  # Python编译文件
            r'\.pyi$',  # Python类型提示文件
        ]
    
    def load_paths(self) -> None:
        """加载路径文件"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.paths = [line.strip() for line in f if line.strip()]
            print(f"成功加载 {len(self.paths)} 个路径")
        except FileNotFoundError:
            print(f"文件 {self.input_file} 不存在")
            return
        except Exception as e:
            print(f"加载文件时出错: {e}")
            return
    
    def is_relevant_path(self, path: str) -> bool:
        """判断路径是否与Augment插件真正相关"""
        # 检查是否应该排除
        for pattern in self.exclude_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return False
        
        # 检查是否包含Augment相关关键词
        augment_patterns = [
            r'augment\.vscode-augment',  # 插件标识符
            r'Augment\.vscode-augment',  # 插件标识符（大写）
            r'\.augment$',  # .augment目录
            r'\.augmentcode',  # .augmentcode目录
            r'augment-modifier-hacker',  # Augment修改器
            r'augment-activation\.json',  # 激活文件
            r'augment-.*\.md$',  # Augment相关markdown文件
            r'augment-.*\.js$',  # Augment相关JS文件
            r'augment-.*\.bat$',  # Augment相关批处理文件
        ]
        
        for pattern in augment_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return True
        
        return False
    
    def categorize_path(self, path: str) -> str:
        """对路径进行分类"""
        path_lower = path.lower()
        
        # 插件安装文件
        if re.search(r'\.vscode\\extensions\\augment\.vscode-augment-[\d.]+', path) or \
           re.search(r'\.cursor\\extensions\\augment\.vscode-augment-[\d.]+', path):
            return "插件安装文件"
        
        # 日志文件
        if 'logs' in path_lower and ('exthost' in path_lower or '.log' in path_lower):
            return "日志文件"
        
        # 缓存文件
        if 'cachedextensionvsixs' in path_lower or 'cache' in path_lower:
            return "缓存文件"
        
        # 用户数据文件
        if 'workspacestorage' in path_lower or 'globalstorage' in path_lower or \
           'augment-user-assets' in path_lower or 'augment-memories' in path_lower or \
           'augment-kv-store' in path_lower or 'augment-global-state' in path_lower:
            return "用户数据文件"
        
        # 配置文件
        if path_lower.endswith('.json') or 'augment-activation' in path_lower:
            return "配置文件"
        
        # 临时文件
        if 'temp' in path_lower or 'tmp' in path_lower or path_lower.endswith('.tmp'):
            return "临时文件"
        
        # 其他相关文件
        return "其他相关文件"
    
    def analyze_paths(self) -> None:
        """分析和分类路径"""
        relevant_paths = []
        
        print("正在过滤和分类路径...")
        for path in self.paths:
            if self.is_relevant_path(path):
                relevant_paths.append(path)
                category = self.categorize_path(path)
                self.categories[category].append(path)
        
        print(f"过滤后剩余 {len(relevant_paths)} 个相关路径")
        
        # 统计各类别数量
        for category, paths in self.categories.items():
            print(f"{category}: {len(paths)} 个")
    
    def get_file_details(self, path: str) -> Dict:
        """获取文件详细信息"""
        details = {
            'path': path,
            'exists': False,
            'size': 0,
            'size_human': '0 B',
            'modified': 'N/A',
            'file_type': 'Unknown'
        }
        
        try:
            if os.path.exists(path):
                details['exists'] = True
                stat = os.stat(path)
                details['size'] = stat.st_size
                details['size_human'] = self.format_size(stat.st_size)
                details['modified'] = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                details['file_type'] = 'Directory' if os.path.isdir(path) else 'File'
        except Exception as e:
            details['error'] = str(e)
        
        return details
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def generate_report(self) -> None:
        """生成详细报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"augment_analysis_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# VSCode Augment插件调研报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 总体统计
            total_relevant = sum(len(paths) for paths in self.categories.values())
            f.write("## 总体统计\n\n")
            f.write(f"- **原始路径总数**: {len(self.paths)}\n")
            f.write(f"- **相关路径总数**: {total_relevant}\n")
            f.write(f"- **过滤掉的路径**: {len(self.paths) - total_relevant}\n\n")
            
            # 各类别详细信息
            for category, paths in self.categories.items():
                if not paths:
                    continue
                    
                f.write(f"## {category} ({len(paths)}个)\n\n")
                
                # 获取重要文件的详细信息
                important_files = []
                for path in paths[:10]:  # 只显示前10个
                    details = self.get_file_details(path)
                    important_files.append(details)
                
                # 写入详细信息表格
                f.write("| 路径 | 类型 | 大小 | 修改时间 | 状态 |\n")
                f.write("|------|------|------|----------|------|\n")
                
                for details in important_files:
                    status = "存在" if details['exists'] else "不存在"
                    f.write(f"| `{details['path']}` | {details['file_type']} | {details['size_human']} | {details['modified']} | {status} |\n")
                
                if len(paths) > 10:
                    f.write(f"\n*还有 {len(paths) - 10} 个文件未显示...*\n")
                
                f.write("\n")
            
            # 关键发现
            f.write("## 关键发现\n\n")
            
            # 插件版本信息
            versions = set()
            for path in self.categories["插件安装文件"]:
                match = re.search(r'augment\.vscode-augment-([\d.]+)', path)
                if match:
                    versions.add(match.group(1))
            
            if versions:
                f.write(f"### 发现的插件版本\n")
                for version in sorted(versions):
                    f.write(f"- v{version}\n")
                f.write("\n")
            
            # 工作区数量
            workspace_count = len([p for p in self.categories["用户数据文件"] if 'workspaceStorage' in p])
            if workspace_count > 0:
                f.write(f"### 工作区使用情况\n")
                f.write(f"- 发现 {workspace_count} 个工作区使用了Augment插件\n\n")
            
            # 存储位置分析
            f.write("### 主要存储位置\n")
            locations = defaultdict(int)
            for category, paths in self.categories.items():
                for path in paths:
                    if 'AppData\\Roaming\\Code' in path:
                        locations['VSCode用户数据'] += 1
                    elif 'AppData\\Roaming\\Cursor' in path:
                        locations['Cursor用户数据'] += 1
                    elif '.vscode\\extensions' in path:
                        locations['VSCode扩展目录'] += 1
                    elif '.cursor\\extensions' in path:
                        locations['Cursor扩展目录'] += 1
                    elif '.augment' in path or '.augmentcode' in path:
                        locations['Augment专用目录'] += 1
            
            for location, count in locations.items():
                f.write(f"- {location}: {count} 个文件/目录\n")
            
            f.write("\n")
            
            # 建议
            f.write("## 建议\n\n")
            f.write("1. **定期清理**: 可以安全删除日志文件和临时文件以释放空间\n")
            f.write("2. **备份重要数据**: 建议备份用户数据文件，特别是包含对话记录的文件\n")
            f.write("3. **版本管理**: 如有多个版本的插件文件，可考虑清理旧版本\n")
            f.write("4. **监控使用**: 定期检查工作区存储使用情况\n\n")
        
        print(f"详细报告已生成: {report_file}")
    
    def run_analysis(self) -> None:
        """执行完整分析"""
        print("=== VSCode Augment插件路径分析 ===")
        
        # 加载路径
        self.load_paths()
        if not self.paths:
            return
        
        # 分析和分类
        self.analyze_paths()
        
        # 生成报告
        self.generate_report()
        
        print("\n=== 分析完成 ===")

if __name__ == "__main__":
    analyzer = AugmentPathAnalyzer()
    analyzer.run_analysis()
