# VSCode Augment插件完全清理工具

## 🎯 功能说明

这个工具基于您的`路径分析.txt`文件，能够**完全清理所有Augment相关数据，但保留插件安装文件**，让您可以继续使用Augment插件，就像全新安装一样。

## 📊 清理范围

### ✅ 将被删除的内容（共291项，540.7 MB）

1. **用户数据文件** (181项, 495.6 MB)
   - 所有对话记录和记忆
   - 工作区数据和设置
   - `.augment` 和 `.augmentcode` 目录
   - 全局存储数据

2. **日志文件** (48项, 34.1 MB)
   - VSCode和Cursor的Augment日志
   - 所有窗口的扩展日志

3. **缓存文件** (1项, 10.7 MB)
   - VSCode扩展缓存

4. **配置文件** (1项, 374 B)
   - Augment激活配置

5. **BlackBox插件中的Augment数据** (50项, 284.5 KB)
   - BlackBox插件存储的Augment相关文件

6. **IntelliJ中的Augment数据** (3项, 0 B)
   - IntelliJ IDEA中的Augment记忆文件

7. **系统相关文件** (7项, 10.0 KB)
   - Windows最近访问记录

### 🛡️ 将被保留的内容

- **插件安装文件**: VSCode和Cursor中的Augment插件本体
- **插件功能**: 清理后可以正常使用Augment的所有功能
- **系统稳定性**: 不会影响VSCode或Cursor的正常运行

## 🚀 使用方法

### 1. 预览清理内容（强烈推荐）
```bash
python augment_complete_cleaner.py --dry-run
```
这会显示所有将要删除的文件，**不会实际删除任何内容**。

### 2. 执行完全清理并备份
```bash
python augment_complete_cleaner.py --backup
```
这会：
- 创建完整备份ZIP文件
- 删除所有Augment数据
- 保留插件安装文件
- 生成详细报告

### 3. 执行完全清理（不备份）
```bash
python augment_complete_cleaner.py
```
直接清理，不创建备份。

## ⚠️ 重要提醒

### 使用前必读
1. **关闭编辑器**: 确保VSCode和Cursor完全关闭
2. **备份重要数据**: 您的`.augmentcode`目录有79.5MB数据，可能包含重要对话记录
3. **预览确认**: 建议先使用`--dry-run`确认清理范围
4. **不可逆转**: 删除操作无法撤销（除非有备份）

### 清理后的效果
- ✅ Augment插件仍然可以正常使用
- ✅ 所有功能都可用（对话、代码生成等）
- ✅ 就像全新安装的Augment一样
- ❌ 所有历史对话记录将丢失
- ❌ 所有个人设置将重置
- ❌ 所有缓存数据将清空

## 📁 生成的文件

1. **备份文件**: `augment_complete_backup_YYYYMMDD_HHMMSS.zip`
   - 包含所有被删除文件的完整备份
   - 按类别组织，便于恢复特定数据

2. **清理报告**: `augment_complete_cleanup_report_YYYYMMDD_HHMMSS.json`
   - 详细的清理统计和操作记录
   - 包含成功/失败的文件列表

## 🔄 数据恢复

如果需要恢复特定数据，可以从备份文件中提取：

```bash
# 解压备份文件
python -m zipfile -e augment_complete_backup_20250902_111255.zip ./restore/

# 手动复制需要的文件回原位置
```

## 💡 使用建议

1. **测试环境**: 如果不确定，建议先在测试环境使用
2. **分类备份**: 可以手动备份重要的对话记录到其他位置
3. **定期清理**: 可以定期使用此工具清理累积的数据
4. **版本控制**: 如果在开发项目中使用，确保项目文件已提交到版本控制

## 🆚 与普通清理工具的区别

| 功能 | 普通清理工具 | 完全清理工具 |
|------|-------------|-------------|
| 数据来源 | 预定义路径模式 | 基于实际扫描结果 |
| 覆盖范围 | 81项, 315.6 MB | 291项, 540.7 MB |
| BlackBox数据 | ❌ 不包含 | ✅ 包含 (50项) |
| IntelliJ数据 | ❌ 不包含 | ✅ 包含 (3项) |
| 准确性 | 基于模式匹配 | 基于实际文件扫描 |

## 🎉 总结

这个完全清理工具确保了：
- **100%覆盖**: 基于实际扫描结果，不遗漏任何相关文件
- **安全保留**: 插件安装文件完全保留，功能不受影响
- **完整备份**: 可选的完整备份机制
- **详细报告**: 清晰的操作记录和统计

**适用场景**:
- 想要重置Augment到全新状态
- 清理累积的大量数据释放空间
- 解决Augment相关的问题
- 保护隐私，清除所有使用记录

---

**最后提醒**: 此工具功能强大且全面，请在充分理解和备份后使用！
