#!/usr/bin/env python3
"""
增强版VSCode Augment插件调研脚本
使用多种方法确保100%准确性和完整性
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Set
import re

class EnhancedAugmentScanner:
    def __init__(self):
        self.results = {
            'files': [],
            'directories': [],
            'registry_entries': [],
            'processes': [],
            'network_connections': []
        }
        
        # 扩展的搜索模式
        self.search_patterns = [
            r'augment',
            r'Augment', 
            r'AUGMENT',
            r'augment-code',
            r'augmentcode',
            r'vscode-augment',
            r'augment\.code',
            r'com\.augment',
            r'augment-extension',
            r'augment-plugin'
        ]
        
        # VSCode相关目录
        self.vscode_dirs = [
            '/mnt/c/Users/<USER>/AppData/Roaming/Code',
            '/mnt/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code',
            '/mnt/c/Users/<USER>/.vscode',
            '/mnt/c/Program Files/Microsoft VS Code',
            '/mnt/c/Program Files (x86)/Microsoft VS Code'
        ]
        
        # 系统关键目录
        self.system_dirs = [
            '/mnt/c/ProgramData',
            '/mnt/c/Windows/System32',
            '/mnt/c/Windows/SysWOW64',
            '/mnt/c/Windows/Temp',
            '/mnt/c/temp',
            '/mnt/c/tmp'
        ]
    
    def run_find_command(self) -> List[str]:
        """使用find命令进行快速搜索"""
        print("使用find命令进行快速搜索...")
        found_items = []
        
        for pattern in ['augment', 'Augment', 'AUGMENT']:
            try:
                # 搜索文件名包含关键词的文件
                cmd = f'find /mnt/c -type f -iname "*{pattern}*" 2>/dev/null'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                if result.stdout:
                    found_items.extend(result.stdout.strip().split('\n'))
                
                # 搜索目录名包含关键词的目录
                cmd = f'find /mnt/c -type d -iname "*{pattern}*" 2>/dev/null'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                if result.stdout:
                    found_items.extend(result.stdout.strip().split('\n'))
                    
            except subprocess.TimeoutExpired:
                print(f"搜索 {pattern} 超时，跳过")
            except Exception as e:
                print(f"搜索 {pattern} 时出错: {e}")
        
        return list(set(filter(None, found_items)))
    
    def run_grep_search(self) -> List[str]:
        """使用grep在配置文件中搜索Augment相关内容"""
        print("在配置文件中搜索Augment相关内容...")
        config_files = []
        
        # 搜索VSCode配置文件
        config_patterns = [
            '/mnt/c/Users/<USER>/AppData/Roaming/Code/User/settings.json',
            '/mnt/c/Users/<USER>/AppData/Roaming/Code/User/extensions.json',
            '/mnt/c/Users/<USER>/.vscode/settings.json',
            '/mnt/c/Users/<USER>/.vscode/extensions.json'
        ]
        
        for pattern in config_patterns:
            try:
                cmd = f'find {os.path.dirname(pattern)} -name "{os.path.basename(pattern)}" 2>/dev/null'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    files = result.stdout.strip().split('\n')
                    for file in files:
                        if file and os.path.exists(file):
                            # 在文件中搜索Augment相关内容
                            try:
                                with open(file, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    for search_pattern in self.search_patterns:
                                        if re.search(search_pattern, content, re.IGNORECASE):
                                            config_files.append(file)
                                            break
                            except Exception as e:
                                print(f"读取文件 {file} 时出错: {e}")
            except Exception as e:
                print(f"搜索配置文件时出错: {e}")
        
        return config_files
    
    def search_registry_like_files(self) -> List[str]:
        """搜索类似注册表的配置文件"""
        print("搜索系统配置文件...")
        registry_files = []
        
        # Windows注册表导出文件和其他配置文件
        search_locations = [
            '/mnt/c/Windows/System32/config',
            '/mnt/c/Users/<USER>/AppData/Local/Microsoft',
            '/mnt/c/ProgramData/Microsoft'
        ]
        
        for location in search_locations:
            try:
                if '*' in location:
                    # 处理通配符路径
                    base_path = location.split('*')[0]
                    if os.path.exists(base_path):
                        for user_dir in os.listdir(base_path):
                            full_path = location.replace('*', user_dir)
                            if os.path.exists(full_path):
                                self._search_in_directory(full_path, registry_files)
                else:
                    if os.path.exists(location):
                        self._search_in_directory(location, registry_files)
            except Exception as e:
                print(f"搜索 {location} 时出错: {e}")
        
        return registry_files
    
    def _search_in_directory(self, directory: str, result_list: List[str]) -> None:
        """在目录中搜索相关文件"""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if self._is_relevant_path(file_path):
                        result_list.append(file_path)
        except PermissionError:
            pass
        except Exception as e:
            print(f"搜索目录 {directory} 时出错: {e}")
    
    def _is_relevant_path(self, path: str) -> bool:
        """检查路径是否相关"""
        path_lower = path.lower()
        for pattern in self.search_patterns:
            if re.search(pattern.lower(), path_lower):
                return True
        return False
    
    def get_detailed_file_info(self, filepath: str) -> Dict:
        """获取文件的详细信息"""
        try:
            stat = os.stat(filepath)
            file_info = {
                'path': filepath,
                'size': stat.st_size,
                'size_human': self._format_size(stat.st_size),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'accessed': datetime.fromtimestamp(stat.st_atime).isoformat(),
                'is_file': os.path.isfile(filepath),
                'is_dir': os.path.isdir(filepath),
                'extension': os.path.splitext(filepath)[1].lower(),
                'permissions': oct(stat.st_mode)[-3:],
                'file_type': self._get_file_type(filepath)
            }
            
            # 如果是文本文件，尝试读取部分内容
            if file_info['is_file'] and self._is_text_file(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content_preview = f.read(1000)  # 读取前1000个字符
                        file_info['content_preview'] = content_preview
                except:
                    pass
            
            return file_info
        except Exception as e:
            return {
                'path': filepath,
                'error': str(e),
                'is_file': os.path.isfile(filepath) if os.path.exists(filepath) else False,
                'is_dir': os.path.isdir(filepath) if os.path.exists(filepath) else False
            }
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def _get_file_type(self, filepath: str) -> str:
        """获取文件类型"""
        ext = os.path.splitext(filepath)[1].lower()
        type_map = {
            '.json': 'JSON配置文件',
            '.js': 'JavaScript文件',
            '.ts': 'TypeScript文件',
            '.log': '日志文件',
            '.txt': '文本文件',
            '.md': 'Markdown文档',
            '.yml': 'YAML配置文件',
            '.yaml': 'YAML配置文件',
            '.xml': 'XML文件',
            '.vsix': 'VSCode扩展包',
            '.exe': '可执行文件',
            '.dll': '动态链接库',
            '.cache': '缓存文件',
            '.tmp': '临时文件',
            '.lock': '锁文件'
        }
        return type_map.get(ext, '未知类型')
    
    def _is_text_file(self, filepath: str) -> bool:
        """判断是否为文本文件"""
        text_extensions = {'.json', '.js', '.ts', '.log', '.txt', '.md', '.yml', '.yaml', '.xml', '.cfg', '.ini'}
        return os.path.splitext(filepath)[1].lower() in text_extensions
    
    def run_comprehensive_scan(self) -> None:
        """执行全面扫描"""
        print("=== 开始VSCode Augment插件全面调研 ===")
        start_time = time.time()
        
        # 方法1: 使用find命令快速搜索
        find_results = self.run_find_command()
        print(f"find命令找到 {len(find_results)} 个项目")
        
        # 方法2: 搜索配置文件内容
        config_results = self.run_grep_search()
        print(f"配置文件搜索找到 {len(config_results)} 个文件")
        
        # 方法3: 搜索系统配置
        registry_results = self.search_registry_like_files()
        print(f"系统配置搜索找到 {len(registry_results)} 个文件")
        
        # 合并所有结果
        all_results = list(set(find_results + config_results + registry_results))
        print(f"总共发现 {len(all_results)} 个相关项目")
        
        # 获取详细信息
        print("正在收集详细信息...")
        for item in all_results:
            if os.path.exists(item):
                file_info = self.get_detailed_file_info(item)
                if file_info['is_file']:
                    self.results['files'].append(file_info)
                elif file_info['is_dir']:
                    self.results['directories'].append(file_info)
        
        # 保存结果
        self.save_results()
        
        end_time = time.time()
        print(f"\n=== 扫描完成 ===")
        print(f"耗时: {end_time - start_time:.2f}秒")
        print(f"发现文件: {len(self.results['files'])}个")
        print(f"发现目录: {len(self.results['directories'])}个")
    
    def save_results(self) -> None:
        """保存扫描结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_augment_scan_{timestamp}.json"
        
        output = {
            'scan_info': {
                'scan_time': datetime.now().isoformat(),
                'search_patterns': self.search_patterns,
                'total_files': len(self.results['files']),
                'total_directories': len(self.results['directories'])
            },
            'results': self.results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
        
        print(f"详细结果已保存到: {filename}")

if __name__ == "__main__":
    scanner = EnhancedAugmentScanner()
    scanner.run_comprehensive_scan()
