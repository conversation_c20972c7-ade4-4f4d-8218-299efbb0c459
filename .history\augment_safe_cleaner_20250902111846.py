#!/usr/bin/env python3
"""
VSCode Augment插件安全清理工具（保护聊天记录数据库）
排除关键数据库路径，确保聊天记录导出功能不受影响

使用方法:
    python augment_safe_cleaner.py --dry-run
    python augment_safe_cleaner.py --backup
    python augment_safe_cleaner.py --help
"""

import os
import sys
import shutil
import argparse
import json
import zipfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set

class AugmentSafeCleaner:
    def __init__(self):
        self.user_home = os.path.expanduser("~")
        self.deleted_files = []
        self.deleted_dirs = []
        self.failed_deletions = []
        self.protected_paths = []
        self.total_size_freed = 0
        self.backup_path = None
        
        # 从路径分析.txt文件中读取所有路径
        self.all_paths = self.load_paths_from_file()
        
        # 定义需要保护的关键路径模式
        self.protected_patterns = [
            # 核心数据库路径（绝对不能删除）
            r'workspaceStorage\.*\Augment.vscode-augment\augment-kv-store',
            # 全局状态数据（可能包含重要配置）
            r'globalStorage\augment.vscode-augment',
            # 工作区根目录（包含数据库）
            r'workspaceStorage\.*\Augment.vscode-augment$',
            # 插件安装文件
            r'\.vscode\extensions\augment\.vscode-augment-',
            r'\.cursor\extensions\augment\.vscode-augment-'
        ]
        
        # 分类所有路径
        self.categorize_safe_paths()
    
    def load_paths_from_file(self) -> List[str]:
        """从路径分析.txt文件加载所有路径"""
        paths_file = "路径分析.txt"
        if not os.path.exists(paths_file):
            print(f"❌ 未找到路径分析文件: {paths_file}")
            return []
        
        try:
            with open(paths_file, 'r', encoding='utf-8') as f:
                paths = [line.strip() for line in f if line.strip()]
            print(f"📁 从 {paths_file} 加载了 {len(paths)} 个路径")
            return paths
        except Exception as e:
            print(f"❌ 读取路径文件失败: {e}")
            return []
    
    def is_protected_path(self, path: str) -> bool:
        """判断路径是否需要保护（不删除）"""
        import re
        
        for pattern in self.protected_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return True
        return False
    
    def should_exclude_path(self, path: str) -> bool:
        """判断是否应该排除此路径（不相关文件）"""
        exclude_patterns = [
            'networkx',
            'torchvision', 
            'zod',
            'Python312\\Lib\\site-packages',
            '__pycache__',
            '.pyc',
            '.pyi',
            'npm-cache',
            'uv\\cache',
            '.langflow-venv'
        ]
        
        path_lower = path.lower()
        return any(pattern.lower() in path_lower for pattern in exclude_patterns)
    
    def categorize_safe_paths(self):
        """将所有路径分类，排除受保护的路径"""
        self.categories = {
            "safe_logs": {
                "description": "日志文件（安全删除）",
                "paths": []
            },
            "safe_cache": {
                "description": "缓存文件（安全删除）",
                "paths": []
            },
            "safe_blackbox": {
                "description": "BlackBox插件中的Augment数据（安全删除）",
                "paths": []
            },
            "safe_system": {
                "description": "系统相关文件（安全删除）",
                "paths": []
            },
            "safe_config": {
                "description": "非关键配置文件（安全删除）",
                "paths": []
            },
            "safe_other": {
                "description": "其他可安全删除的文件",
                "paths": []
            }
        }
        
        protected_count = 0
        
        for path in self.all_paths:
            # 跳过不相关的文件
            if self.should_exclude_path(path):
                continue
            
            # 检查是否为受保护的路径
            if self.is_protected_path(path):
                self.protected_paths.append(path)
                protected_count += 1
                continue
            
            # 分类可安全删除的路径
            category = self.classify_safe_path(path)
            if category:
                self.categories[category]["paths"].append(path)
        
        print(f"🛡️  保护了 {protected_count} 个关键路径")
    
    def classify_safe_path(self, path: str) -> str:
        """对可安全删除的路径进行分类"""
        path_lower = path.lower()
        
        # 日志文件（安全删除）
        if 'logs' in path_lower and 'exthost' in path_lower and 'augment.vscode-augment' in path_lower:
            return "safe_logs"
        
        # 缓存文件（安全删除）
        if 'cachedextensionvsixs' in path_lower and 'augment.vscode-augment' in path_lower:
            return "safe_cache"
        
        # BlackBox数据（安全删除）
        if 'blackboxapp.blackbox' in path_lower and 'augment' in path_lower:
            return "safe_blackbox"
        
        # 系统文件（安全删除）
        if 'recent' in path_lower and '.lnk' in path_lower and 'augment' in path_lower:
            return "safe_system"
        
        # 非关键配置文件（安全删除）
        if 'augment-activation.json' in path_lower or 'augment-modifier-hacker' in path_lower:
            return "safe_config"
        
        # 用户资源文件（但不是核心数据库）
        if 'augment-user-assets' in path_lower:
            return "safe_other"
        
        # IntelliJ数据（安全删除）
        if '.augmentcode' in path and 'intellij' in path_lower:
            return "safe_other"
        
        # 其他Augment相关但非核心的文件
        if 'augment' in path_lower:
            return "safe_other"
        
        return None
    
    def get_file_size(self, path: str) -> int:
        """获取文件或目录大小"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            continue
                return total_size
        except (OSError, FileNotFoundError):
            pass
        return 0
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def preview_safe_deletion(self) -> None:
        """预览安全删除的文件"""
        print("\n" + "="*80)
        print("📋 安全清理预览（保护聊天记录数据库）")
        print("="*80)
        
        # 显示受保护的路径
        if self.protected_paths:
            print(f"\n🛡️  受保护的路径（将被保留）:")
            print("-" * 50)
            protected_existing = [p for p in self.protected_paths if os.path.exists(p)]
            for i, path in enumerate(protected_existing[:10]):
                size = self.get_file_size(path)
                file_type = "📁" if os.path.isdir(path) else "📄"
                print(f"  {file_type} {path} ({self.format_size(size)})")
            
            if len(protected_existing) > 10:
                print(f"  ... 还有 {len(protected_existing) - 10} 个受保护的路径")
            
            protected_size = sum(self.get_file_size(p) for p in protected_existing)
            print(f"  🛡️  保护数据总计: {len(protected_existing)} 项, {self.format_size(protected_size)}")
        
        # 显示将要删除的文件
        total_files = 0
        total_size = 0
        
        for category, info in self.categories.items():
            paths = [p for p in info["paths"] if os.path.exists(p)]
            if not paths:
                continue
                
            print(f"\n📁 {info['description']}:")
            print("-" * 50)
            
            category_size = 0
            category_count = 0
            
            # 显示前5个文件
            for i, path in enumerate(paths[:5]):
                size = self.get_file_size(path)
                file_type = "📁" if os.path.isdir(path) else "📄"
                print(f"  {file_type} {path} ({self.format_size(size)})")
                category_size += size
                category_count += 1
            
            # 如果有更多文件，显示统计
            if len(paths) > 5:
                remaining_size = sum(self.get_file_size(p) for p in paths[5:])
                category_size += remaining_size
                category_count = len(paths)
                print(f"  ... 还有 {len(paths) - 5} 个文件")
            
            print(f"  📊 小计: {category_count} 项, {self.format_size(category_size)}")
            total_files += category_count
            total_size += category_size
        
        print(f"\n🎯 安全删除总计: {total_files} 项, {self.format_size(total_size)}")
        print("="*80)
        print("✅ 聊天记录数据库已受保护，导出功能不会受影响")
        print("✅ 插件安装文件已受保护，Augment功能正常")
        print("="*80)
