#!/usr/bin/env python3
"""
VSCode Augment插件清理工具测试脚本
用于验证清理工具的功能和安全性
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from augment_cleaner import AugmentCleaner
except ImportError:
    print("❌ 无法导入 augment_cleaner 模块")
    print("请确保 augment_cleaner.py 文件在当前目录中")
    sys.exit(1)

class AugmentCleanerTester:
    def __init__(self):
        self.test_dir = None
        self.original_home = None
        
    def setup_test_environment(self):
        """创建测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp(prefix="augment_test_")
        print(f"📁 测试目录: {self.test_dir}")
        
        # 创建模拟的文件结构
        test_files = [
            # VSCode用户数据
            "AppData/Roaming/Code/User/globalStorage/augment.vscode-augment/test.json",
            "AppData/Roaming/Code/User/workspaceStorage/abc123/Augment.vscode-augment/augment-global-state/state.json",
            "AppData/Roaming/Code/User/workspaceStorage/abc123/Augment.vscode-augment/augment-user-assets/asset.txt",
            "AppData/Roaming/Code/User/workspaceStorage/abc123/Augment.vscode-augment/Augment-Memories/memory.md",
            
            # VSCode日志
            "AppData/Roaming/Code/logs/20250901T154452/window1/exthost/Augment.vscode-augment/Augment.log",
            "AppData/Roaming/Code/logs/20250901T154452/window2/exthost/Augment.vscode-augment/Augment.log",
            
            # VSCode缓存
            "AppData/Roaming/Code/CachedExtensionVSIXs/augment.vscode-augment-0.540.2",
            
            # Cursor数据
            "AppData/Roaming/Cursor/User/workspaceStorage/def456/Augment.vscode-augment/augment-user-assets/cursor_asset.txt",
            "AppData/Roaming/Cursor/logs/20250804T080708/window1/exthost/Augment.vscode-augment/Augment.log",
            
            # 配置文件
            "AppData/Roaming/augment-modifier-hacker/augment-activation.json",
            
            # 扩展文件
            ".vscode/extensions/augment.vscode-augment-0.540.2/package.json",
            ".vscode/extensions/augment.vscode-augment-0.540.2/extension.js",
            ".cursor/extensions/augment.vscode-augment-0.492.2/package.json",
            
            # Augment专用目录
            ".augment/config.json",
            ".augmentcode/data.json",
            
            # 系统文件
            "AppData/Roaming/Microsoft/Windows/Recent/augment.vscode-augment.lnk",
        ]
        
        # 创建测试文件
        for file_path in test_files:
            full_path = os.path.join(self.test_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # 创建文件内容
            if file_path.endswith('.json'):
                content = '{"test": "data", "created_by": "test_script"}'
            elif file_path.endswith('.log'):
                content = '[2025-09-02 10:00:00] Test log entry\n[2025-09-02 10:01:00] Another log entry'
            elif file_path.endswith('.md'):
                content = '# Test Memory\n\nThis is a test conversation memory.'
            else:
                content = f'Test file content for {os.path.basename(file_path)}'
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        print(f"✅ 创建了 {len(test_files)} 个测试文件")
        
        # 备份原始用户目录并设置测试目录
        self.original_home = os.path.expanduser("~")
        
        return self.test_dir
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        if self.test_dir and os.path.exists(self.test_dir):
            print(f"🧹 清理测试目录: {self.test_dir}")
            shutil.rmtree(self.test_dir)
    
    def count_files_in_directory(self, directory):
        """统计目录中的文件数量"""
        if not os.path.exists(directory):
            return 0
        
        count = 0
        for root, dirs, files in os.walk(directory):
            count += len(files)
        return count
    
    def test_dry_run_mode(self):
        """测试干运行模式"""
        print("\n" + "="*50)
        print("🧪 测试干运行模式")
        print("="*50)
        
        # 创建清理器实例并修改用户目录
        cleaner = AugmentCleaner()
        cleaner.user_home = self.test_dir
        
        # 测试用户数据模式
        print("\n📋 测试用户数据清理预览...")
        files_to_delete = cleaner.scan_files("user-data")
        
        total_files = sum(len(paths) for paths in files_to_delete.values())
        print(f"发现 {total_files} 个项目")
        
        # 执行预览
        cleaner.preview_deletion(files_to_delete)
        
        return total_files > 0
    
    def test_backup_functionality(self):
        """测试备份功能"""
        print("\n" + "="*50)
        print("🧪 测试备份功能")
        print("="*50)
        
        cleaner = AugmentCleaner()
        cleaner.user_home = self.test_dir
        
        files_to_delete = cleaner.scan_files("user-data")
        backup_path = cleaner.create_backup(files_to_delete)
        
        if backup_path and os.path.exists(backup_path):
            backup_size = os.path.getsize(backup_path)
            print(f"✅ 备份创建成功: {backup_path} ({backup_size} bytes)")
            
            # 清理备份文件
            os.remove(backup_path)
            return True
        else:
            print("❌ 备份创建失败")
            return False
    
    def test_file_scanning(self):
        """测试文件扫描功能"""
        print("\n" + "="*50)
        print("🧪 测试文件扫描功能")
        print("="*50)
        
        cleaner = AugmentCleaner()
        cleaner.user_home = self.test_dir
        
        # 测试用户数据模式
        user_data_files = cleaner.scan_files("user-data")
        user_data_count = sum(len(paths) for paths in user_data_files.values())
        
        # 测试完全清理模式
        complete_files = cleaner.scan_files("complete")
        complete_count = sum(len(paths) for paths in complete_files.values())
        
        print(f"用户数据模式: {user_data_count} 个文件")
        print(f"完全清理模式: {complete_count} 个文件")
        
        # 完全清理应该包含更多文件（包括扩展文件）
        return complete_count >= user_data_count
    
    def test_actual_deletion(self):
        """测试实际删除功能（在测试环境中）"""
        print("\n" + "="*50)
        print("🧪 测试实际删除功能")
        print("="*50)
        
        # 统计删除前的文件数量
        files_before = self.count_files_in_directory(self.test_dir)
        print(f"删除前文件总数: {files_before}")
        
        cleaner = AugmentCleaner()
        cleaner.user_home = self.test_dir
        
        # 执行用户数据清理（不创建备份，因为是测试）
        files_to_delete = cleaner.scan_files("user-data")
        cleaner.perform_deletion(files_to_delete, dry_run=False)
        
        # 统计删除后的文件数量
        files_after = self.count_files_in_directory(self.test_dir)
        print(f"删除后文件总数: {files_after}")
        
        deleted_count = files_before - files_after
        print(f"实际删除文件数: {deleted_count}")
        print(f"清理器统计删除: {len(cleaner.deleted_files)} 个文件, {len(cleaner.deleted_dirs)} 个目录")
        
        return deleted_count > 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 VSCode Augment清理工具测试套件")
        print("="*60)
        
        test_results = {}
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行测试
            test_results["dry_run"] = self.test_dry_run_mode()
            test_results["backup"] = self.test_backup_functionality()
            test_results["scanning"] = self.test_file_scanning()
            test_results["deletion"] = self.test_actual_deletion()
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            test_results["error"] = str(e)
        
        finally:
            # 清理测试环境
            self.cleanup_test_environment()
        
        # 显示测试结果
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        passed = 0
        total = 0
        
        for test_name, result in test_results.items():
            if test_name == "error":
                continue
            
            total += 1
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name.ljust(15)}: {status}")
            if result:
                passed += 1
        
        if "error" in test_results:
            print(f"错误信息: {test_results['error']}")
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！清理工具可以安全使用。")
        else:
            print("⚠️  部分测试失败，请检查清理工具代码。")
        
        return passed == total


def main():
    """主函数"""
    print("开始测试VSCode Augment清理工具...")
    
    tester = AugmentCleanerTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
