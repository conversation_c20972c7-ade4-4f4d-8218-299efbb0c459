#!/usr/bin/env python3
"""
VSCode Augment插件清理工具安全验证脚本
仅验证功能，不执行任何删除操作
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from augment_cleaner import AugmentCleaner
except ImportError:
    print("❌ 无法导入 augment_cleaner 模块")
    print("请确保 augment_cleaner.py 文件在当前目录中")
    sys.exit(1)

class SafeCleanerVerifier:
    def __init__(self):
        self.test_passed = 0
        self.test_total = 0
    
    def test_import_and_initialization(self):
        """测试模块导入和初始化"""
        print("🧪 测试1: 模块导入和初始化")
        try:
            cleaner = AugmentCleaner()
            print(f"  ✅ 成功创建清理器实例")
            print(f"  ✅ 用户目录: {cleaner.user_home}")
            print(f"  ✅ 发现 {len(cleaner.file_categories)} 个文件类别")
            self.test_passed += 1
        except Exception as e:
            print(f"  ❌ 初始化失败: {e}")
        self.test_total += 1
    
    def test_file_categories(self):
        """测试文件分类定义"""
        print("\n🧪 测试2: 文件分类定义")
        cleaner = AugmentCleaner()
        
        expected_categories = ["user_data", "logs", "cache", "config", "extensions", "system"]
        
        for category in expected_categories:
            if category in cleaner.file_categories:
                paths_count = len(cleaner.file_categories[category]["paths"])
                print(f"  ✅ {category}: {paths_count} 个路径模式")
            else:
                print(f"  ❌ 缺少类别: {category}")
                return
        
        self.test_passed += 1
        self.test_total += 1
    
    def test_path_expansion_safe(self):
        """安全测试路径展开功能（不访问真实文件）"""
        print("\n🧪 测试3: 路径展开功能（安全模式）")
        cleaner = AugmentCleaner()
        
        # 测试不存在的路径
        test_patterns = [
            "/nonexistent/path/test",
            "/nonexistent/path/*/test",
            "C:\\NonExistent\\Path\\test.txt"
        ]
        
        try:
            expanded = cleaner.expand_paths(test_patterns)
            print(f"  ✅ 路径展开功能正常，返回 {len(expanded)} 个路径")
            self.test_passed += 1
        except Exception as e:
            print(f"  ❌ 路径展开失败: {e}")
        
        self.test_total += 1
    
    def test_size_formatting(self):
        """测试文件大小格式化"""
        print("\n🧪 测试4: 文件大小格式化")
        cleaner = AugmentCleaner()
        
        test_cases = [
            (0, "0.0 B"),
            (512, "512.0 B"),
            (1024, "1.0 KB"),
            (1048576, "1.0 MB"),
            (1073741824, "1.0 GB")
        ]
        
        all_passed = True
        for size, expected in test_cases:
            result = cleaner.format_size(size)
            if result == expected:
                print(f"  ✅ {size} bytes -> {result}")
            else:
                print(f"  ❌ {size} bytes -> {result} (期望: {expected})")
                all_passed = False
        
        if all_passed:
            self.test_passed += 1
        self.test_total += 1
    
    def test_scan_modes(self):
        """测试扫描模式（不访问真实文件）"""
        print("\n🧪 测试5: 扫描模式定义")
        cleaner = AugmentCleaner()
        
        try:
            # 测试用户数据模式
            user_data_categories = ["user_data", "logs", "cache", "config", "system"]
            print(f"  ✅ 用户数据模式包含: {', '.join(user_data_categories)}")
            
            # 测试完全清理模式
            complete_categories = list(cleaner.file_categories.keys())
            print(f"  ✅ 完全清理模式包含: {', '.join(complete_categories)}")
            
            # 验证完全清理包含扩展文件
            if "extensions" in complete_categories:
                print(f"  ✅ 完全清理模式包含插件安装文件")
            else:
                print(f"  ❌ 完全清理模式缺少插件安装文件")
                return
            
            self.test_passed += 1
        except Exception as e:
            print(f"  ❌ 扫描模式测试失败: {e}")
        
        self.test_total += 1
    
    def test_dry_run_safety(self):
        """验证干运行模式的安全性"""
        print("\n🧪 测试6: 干运行模式安全性")
        cleaner = AugmentCleaner()
        
        # 创建一个临时文件用于测试
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = tmp_file.name
            tmp_file.write(b"test content")
        
        try:
            # 测试干运行模式不会删除文件
            success, size = cleaner.delete_file_or_dir(tmp_path, dry_run=True)
            
            if os.path.exists(tmp_path):
                print(f"  ✅ 干运行模式安全：文件未被删除")
                print(f"  ✅ 返回大小信息: {size} bytes")
                self.test_passed += 1
            else:
                print(f"  ❌ 干运行模式不安全：文件被意外删除")
        
        except Exception as e:
            print(f"  ❌ 干运行测试失败: {e}")
        
        finally:
            # 清理测试文件
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
        
        self.test_total += 1
    
    def test_real_file_detection(self):
        """测试真实文件检测（仅检测，不操作）"""
        print("\n🧪 测试7: 真实文件检测")
        cleaner = AugmentCleaner()
        
        # 检查是否能正确识别真实存在的Augment文件
        real_paths_found = 0
        
        for category, info in cleaner.file_categories.items():
            expanded_paths = cleaner.expand_paths(info["paths"])
            if expanded_paths:
                real_paths_found += len(expanded_paths)
                print(f"  📁 {category}: 发现 {len(expanded_paths)} 个真实路径")
        
        print(f"  📊 总计发现 {real_paths_found} 个真实的Augment相关路径")
        
        if real_paths_found > 0:
            print(f"  ✅ 文件检测功能正常")
            self.test_passed += 1
        else:
            print(f"  ℹ️  未发现Augment文件（可能已被清理或未安装）")
            self.test_passed += 1  # 这也是正常情况
        
        self.test_total += 1
    
    def run_verification(self):
        """运行所有验证测试"""
        print("🔍 VSCode Augment清理工具安全验证")
        print("="*60)
        print("⚠️  注意：此验证脚本不会删除任何文件")
        print("="*60)
        
        # 运行所有测试
        self.test_import_and_initialization()
        self.test_file_categories()
        self.test_path_expansion_safe()
        self.test_size_formatting()
        self.test_scan_modes()
        self.test_dry_run_safety()
        self.test_real_file_detection()
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 验证结果汇总")
        print("="*60)
        
        success_rate = (self.test_passed / self.test_total) * 100 if self.test_total > 0 else 0
        
        print(f"通过测试: {self.test_passed}/{self.test_total} ({success_rate:.1f}%)")
        
        if self.test_passed == self.test_total:
            print("🎉 所有验证通过！清理工具功能正常。")
            print("\n💡 使用建议:")
            print("   1. 先使用 --dry-run 预览要删除的文件")
            print("   2. 使用 --backup 创建备份")
            print("   3. 确保VSCode和Cursor已关闭")
        else:
            print("⚠️  部分验证失败，请检查清理工具。")
        
        return self.test_passed == self.test_total


def main():
    """主函数"""
    verifier = SafeCleanerVerifier()
    success = verifier.run_verification()
    
    print(f"\n{'='*60}")
    print("🛡️  安全保证:")
    print("   - 此验证脚本未执行任何删除操作")
    print("   - 所有测试都是只读的")
    print("   - 您的文件完全安全")
    print("="*60)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
