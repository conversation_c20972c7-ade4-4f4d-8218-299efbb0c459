#!/usr/bin/env python3
"""
VSCode Augment插件全面调研脚本
扫描C盘中所有与Augment插件相关的文件和目录
"""

import os
import sys
import json
import time
import hashlib
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Set

class AugmentScanner:
    def __init__(self):
        self.results = []
        self.search_patterns = [
            'augment',
            'Augment', 
            'AUGMENT',
            'augment-code',
            'augmentcode',
            'vscode-augment'
        ]
        
        # 重点搜索目录
        self.priority_dirs = [
            '/mnt/c/Users',
            '/mnt/c/Program Files',
            '/mnt/c/Program Files (x86)',
            '/mnt/c/ProgramData',
            '/mnt/c/Windows/Temp',
            '/mnt/c/temp',
            '/mnt/c/tmp'
        ]
        
        # 文件扩展名过滤
        self.relevant_extensions = {
            '.json', '.js', '.ts', '.log', '.txt', '.md', '.yml', '.yaml',
            '.config', '.cfg', '.ini', '.xml', '.vsix', '.exe', '.dll',
            '.cache', '.tmp', '.lock', '.pid'
        }
        
    def is_relevant_file(self, filepath: str) -> bool:
        """判断文件是否与Augment相关"""
        filename = os.path.basename(filepath).lower()
        
        # 检查文件名是否包含关键词
        for pattern in self.search_patterns:
            if pattern.lower() in filename:
                return True
                
        # 检查路径是否包含关键词
        path_lower = filepath.lower()
        for pattern in self.search_patterns:
            if pattern.lower() in path_lower:
                return True
                
        return False
    
    def get_file_info(self, filepath: str) -> Dict:
        """获取文件详细信息"""
        try:
            stat = os.stat(filepath)
            return {
                'path': filepath,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'is_file': os.path.isfile(filepath),
                'is_dir': os.path.isdir(filepath),
                'extension': os.path.splitext(filepath)[1].lower(),
                'permissions': oct(stat.st_mode)[-3:]
            }
        except Exception as e:
            return {
                'path': filepath,
                'error': str(e),
                'is_file': os.path.isfile(filepath),
                'is_dir': os.path.isdir(filepath)
            }
    
    def scan_directory(self, directory: str, max_depth: int = 10) -> None:
        """递归扫描目录"""
        if max_depth <= 0:
            return
            
        try:
            print(f"正在扫描: {directory}")
            
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                try:
                    if self.is_relevant_file(item_path):
                        file_info = self.get_file_info(item_path)
                        self.results.append(file_info)
                        print(f"发现相关文件: {item_path}")
                    
                    # 如果是目录，递归扫描
                    if os.path.isdir(item_path) and not os.path.islink(item_path):
                        self.scan_directory(item_path, max_depth - 1)
                        
                except PermissionError:
                    print(f"权限不足，跳过: {item_path}")
                    continue
                except Exception as e:
                    print(f"处理文件时出错 {item_path}: {e}")
                    continue
                    
        except PermissionError:
            print(f"权限不足，跳过目录: {directory}")
        except Exception as e:
            print(f"扫描目录时出错 {directory}: {e}")
    
    def run_scan(self) -> None:
        """执行完整扫描"""
        print("开始VSCode Augment插件全面调研...")
        print(f"搜索关键词: {', '.join(self.search_patterns)}")
        
        start_time = time.time()
        
        for directory in self.priority_dirs:
            if os.path.exists(directory):
                print(f"\n=== 扫描优先目录: {directory} ===")
                self.scan_directory(directory)
            else:
                print(f"目录不存在: {directory}")
        
        # 保存结果
        self.save_results()
        
        end_time = time.time()
        print(f"\n扫描完成！耗时: {end_time - start_time:.2f}秒")
        print(f"发现相关文件/目录: {len(self.results)}个")
    
    def save_results(self) -> None:
        """保存扫描结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"augment_scan_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scan_time': datetime.now().isoformat(),
                'total_found': len(self.results),
                'search_patterns': self.search_patterns,
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到: {filename}")

if __name__ == "__main__":
    scanner = AugmentScanner()
    scanner.run_scan()
