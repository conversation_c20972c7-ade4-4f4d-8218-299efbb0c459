#!/usr/bin/env python3
"""
VSCode Augment插件验证脚本
使用多种方法交叉验证结果的完整性
"""

import os
import json
import subprocess
from datetime import datetime
from typing import List, Dict, Set

class AugmentVerification:
    def __init__(self):
        self.verification_results = {
            'registry_search': [],
            'file_content_search': [],
            'process_search': [],
            'additional_locations': []
        }
        
    def search_windows_registry(self) -> List[str]:
        """搜索Windows注册表中的Augment相关项"""
        print("搜索Windows注册表...")
        registry_results = []
        
        # 使用reg query命令搜索注册表
        registry_keys = [
            r"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall",
            r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"HKEY_CURRENT_USER\Software\Classes",
            r"HKEY_CURRENT_USER\Software"
        ]
        
        for key in registry_keys:
            try:
                # 搜索包含augment的注册表项
                cmd = f'reg query "{key}" /s /f "augment" 2>nul'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and 'augment' in line.lower():
                            registry_results.append(line.strip())
            except subprocess.TimeoutExpired:
                print(f"搜索注册表 {key} 超时")
            except Exception as e:
                print(f"搜索注册表 {key} 时出错: {e}")
        
        return registry_results
    
    def search_file_contents(self) -> List[str]:
        """在配置文件中搜索Augment相关内容"""
        print("搜索文件内容...")
        content_results = []
        
        # 搜索VSCode和Cursor的配置文件
        config_locations = [
            r"C:\Users\<USER>\AppData\Roaming\Code\User\settings.json",
            r"C:\Users\<USER>\AppData\Roaming\Code\User\extensions.json",
            r"C:\Users\<USER>\AppData\Roaming\Cursor\User\settings.json",
            r"C:\Users\<USER>\AppData\Roaming\Cursor\User\extensions.json",
            r"C:\Users\<USER>\.vscode\settings.json",
            r"C:\Users\<USER>\.cursor\settings.json"
        ]
        
        for config_file in config_locations:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'augment' in content.lower():
                            content_results.append(config_file)
                            print(f"在 {config_file} 中发现Augment相关内容")
                except Exception as e:
                    print(f"读取 {config_file} 时出错: {e}")
        
        return content_results
    
    def search_running_processes(self) -> List[str]:
        """搜索正在运行的Augment相关进程"""
        print("搜索运行中的进程...")
        process_results = []
        
        try:
            # 使用tasklist命令搜索进程
            cmd = 'tasklist /fo csv | findstr /i "augment"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        process_results.append(line.strip())
        except Exception as e:
            print(f"搜索进程时出错: {e}")
        
        return process_results
    
    def search_additional_locations(self) -> List[str]:
        """搜索其他可能的位置"""
        print("搜索其他可能位置...")
        additional_results = []
        
        # 其他可能的位置
        additional_paths = [
            r"C:\ProgramData",
            r"C:\Program Files\Common Files",
            r"C:\Windows\System32\config\systemprofile",
            r"C:\Users\<USER>\Documents",
            r"C:\Users\<USER>\Desktop",
            r"C:\Users\<USER>\Downloads"
        ]
        
        for base_path in additional_paths:
            if os.path.exists(base_path):
                try:
                    for root, dirs, files in os.walk(base_path):
                        # 检查目录名
                        for dir_name in dirs:
                            if 'augment' in dir_name.lower():
                                full_path = os.path.join(root, dir_name)
                                additional_results.append(full_path)
                        
                        # 检查文件名
                        for file_name in files:
                            if 'augment' in file_name.lower():
                                full_path = os.path.join(root, file_name)
                                additional_results.append(full_path)
                        
                        # 限制搜索深度避免过深
                        if root.count(os.sep) - base_path.count(os.sep) > 3:
                            dirs.clear()
                            
                except PermissionError:
                    continue
                except Exception as e:
                    print(f"搜索 {base_path} 时出错: {e}")
                    continue
        
        return additional_results
    
    def check_file_sizes(self) -> Dict:
        """检查重要文件的大小"""
        print("检查文件大小...")
        size_info = {}
        
        important_paths = [
            r"C:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.540.2",
            r"C:\Users\<USER>\.cursor\extensions\augment.vscode-augment-0.492.2",
            r"C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment",
            r"C:\Users\<USER>\AppData\Roaming\Code\CachedExtensionVSIXs\augment.vscode-augment-0.540.2"
        ]
        
        for path in important_paths:
            if os.path.exists(path):
                try:
                    if os.path.isfile(path):
                        size = os.path.getsize(path)
                        size_info[path] = {
                            'size_bytes': size,
                            'size_human': self.format_size(size),
                            'type': 'file'
                        }
                    elif os.path.isdir(path):
                        total_size = self.get_directory_size(path)
                        size_info[path] = {
                            'size_bytes': total_size,
                            'size_human': self.format_size(total_size),
                            'type': 'directory'
                        }
                except Exception as e:
                    size_info[path] = {'error': str(e)}
        
        return size_info
    
    def get_directory_size(self, path: str) -> int:
        """获取目录总大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        continue
        except Exception:
            pass
        return total_size
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def run_verification(self) -> None:
        """执行完整验证"""
        print("=== 开始交叉验证 ===")
        
        # 搜索注册表
        self.verification_results['registry_search'] = self.search_windows_registry()
        
        # 搜索文件内容
        self.verification_results['file_content_search'] = self.search_file_contents()
        
        # 搜索运行进程
        self.verification_results['process_search'] = self.search_running_processes()
        
        # 搜索其他位置
        self.verification_results['additional_locations'] = self.search_additional_locations()
        
        # 检查文件大小
        size_info = self.check_file_sizes()
        
        # 保存验证结果
        self.save_verification_results(size_info)
        
        print("=== 验证完成 ===")
    
    def save_verification_results(self, size_info: Dict) -> None:
        """保存验证结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"augment_verification_{timestamp}.json"
        
        results = {
            'verification_time': datetime.now().isoformat(),
            'registry_entries': len(self.verification_results['registry_search']),
            'config_files_with_augment': len(self.verification_results['file_content_search']),
            'running_processes': len(self.verification_results['process_search']),
            'additional_locations': len(self.verification_results['additional_locations']),
            'details': self.verification_results,
            'file_sizes': size_info
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"验证结果已保存到: {filename}")
        
        # 打印摘要
        print("\n=== 验证摘要 ===")
        print(f"注册表项: {results['registry_entries']} 个")
        print(f"配置文件: {results['config_files_with_augment']} 个")
        print(f"运行进程: {results['running_processes']} 个")
        print(f"额外位置: {results['additional_locations']} 个")
        
        if size_info:
            print("\n=== 重要文件大小 ===")
            for path, info in size_info.items():
                if 'error' not in info:
                    print(f"{os.path.basename(path)}: {info['size_human']}")

if __name__ == "__main__":
    verifier = AugmentVerification()
    verifier.run_verification()
