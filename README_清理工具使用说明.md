# VSCode Augment插件清理工具使用说明

## 🚨 重要安全警告

**在使用此工具前，请务必阅读以下警告：**

1. **此工具将永久删除VSCode Augment插件相关文件**
2. **删除操作不可逆转，请确保已备份重要数据**
3. **建议先使用 `--dry-run` 参数预览要删除的文件**
4. **确保VSCode和Cursor已完全关闭后再运行**
5. **重要的对话记录和用户数据将被删除**

## 📋 功能特性

- ✅ **两种清理模式**：用户数据清理 / 完全清理
- ✅ **安全预览**：干运行模式预览删除文件
- ✅ **自动备份**：删除前创建完整备份
- ✅ **详细日志**：显示每个删除操作的详细信息
- ✅ **错误处理**：安全的错误处理和回滚机制
- ✅ **统计报告**：生成详细的清理报告
- ✅ **进程检查**：自动检查VSCode/Cursor是否在运行

## 🛠️ 系统要求

- Python 3.6+
- Windows 10/11
- 管理员权限（推荐）

## 📦 清理范围

### 模式A：用户数据清理 (`--mode user-data`)

**保留插件本身，仅清理用户数据**

| 类别 | 描述 | 示例路径 |
|------|------|----------|
| 用户数据 | 对话记录、工作区数据 | `AppData\Roaming\Code\User\globalStorage\augment.vscode-augment` |
| 日志文件 | VSCode/Cursor日志 | `AppData\Roaming\Code\logs\*\exthost\Augment.vscode-augment` |
| 缓存文件 | 扩展缓存 | `AppData\Roaming\Code\CachedExtensionVSIXs\augment.*` |
| 配置文件 | 激活配置 | `AppData\Roaming\augment-modifier-hacker\augment-activation.json` |
| 系统文件 | 快捷方式等 | `AppData\Roaming\Microsoft\Windows\Recent\*augment*.lnk` |

### 模式B：完全清理 (`--mode complete`)

**删除插件本身和所有相关数据**

包含模式A的所有内容，另外还包括：

| 类别 | 描述 | 示例路径 |
|------|------|----------|
| 插件安装文件 | VSCode扩展目录 | `.vscode\extensions\augment.vscode-augment-*` |
| 插件安装文件 | Cursor扩展目录 | `.cursor\extensions\augment.vscode-augment-*` |

## 🚀 使用方法

### 1. 预览删除（推荐第一步）

```bash
# 预览用户数据清理
python augment_cleaner.py --mode user-data --dry-run

# 预览完全清理
python augment_cleaner.py --mode complete --dry-run
```

### 2. 执行清理

```bash
# 清理用户数据并创建备份
python augment_cleaner.py --mode user-data --backup

# 完全清理并创建备份
python augment_cleaner.py --mode complete --backup

# 仅清理用户数据（不备份）
python augment_cleaner.py --mode user-data
```

### 3. 查看帮助

```bash
python augment_cleaner.py --help
```

## 📊 输出示例

### 预览模式输出
```
🧹 VSCode Augment插件清理工具
============================================================
🔍 扫描模式: user-data
📊 发现 156 个项目需要处理

============================================================
📋 删除预览 (--dry-run 模式)
============================================================

📁 用户数据文件（对话记录、工作区数据等）:
----------------------------------------
  📁 C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment (3.7 KB)
  📁 C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\abc123\Augment.vscode-augment (15.2 KB)
  小计: 45 项, 2.3 MB

📁 日志文件:
----------------------------------------
  📁 C:\Users\<USER>\AppData\Roaming\Code\logs\20250901T154452\window1\exthost\Augment.vscode-augment (1.2 KB)
  小计: 48 项, 156.7 KB

📊 总计: 156 项, 15.8 MB
============================================================
```

### 实际清理输出
```
🔄 正在创建备份到 augment_backup_20250902_105030.zip...
✅ 备份完成: 156 个文件, 大小: 12.3 MB

============================================================
🗑️  开始删除操作
============================================================

🔄 正在清理: 用户数据文件（对话记录、工作区数据等）
  ✅ 已删除 📁 C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment (3.7 KB)
  ✅ 已删除 📁 C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\abc123\Augment.vscode-augment (15.2 KB)

============================================================
📊 清理完成统计
============================================================
✅ 已删除文件: 89 个
✅ 已删除目录: 67 个
❌ 删除失败: 0 个
💾 释放空间: 15.8 MB

📄 清理报告已保存: augment_cleanup_report_20250902_105030.json
```

## 📁 生成的文件

### 1. 备份文件
- **文件名**: `augment_backup_YYYYMMDD_HHMMSS.zip`
- **内容**: 所有将要删除的文件的完整备份
- **结构**: 按类别组织的目录结构

### 2. 清理报告
- **文件名**: `augment_cleanup_report_YYYYMMDD_HHMMSS.json`
- **内容**: 详细的清理统计和操作记录
- **格式**: JSON格式，便于程序处理

## ⚠️ 注意事项

### 使用前检查
1. **关闭编辑器**: 确保VSCode和Cursor完全关闭
2. **备份数据**: 重要对话记录请手动备份
3. **预览删除**: 使用 `--dry-run` 确认删除范围
4. **管理员权限**: 某些系统文件可能需要管理员权限

### 安全建议
1. **测试环境**: 建议先在测试环境中使用
2. **分步执行**: 可以先执行用户数据清理，再考虑完全清理
3. **保留备份**: 清理后保留备份文件一段时间
4. **验证结果**: 清理后检查VSCode是否正常工作

### 故障排除
1. **权限不足**: 以管理员身份运行命令提示符
2. **文件占用**: 确保相关进程已完全关闭
3. **路径不存在**: 某些路径可能因系统差异而不存在（正常现象）

## 🔄 恢复数据

如果需要恢复数据，可以从备份文件中提取：

```bash
# 解压备份文件
python -m zipfile -e augment_backup_20250902_105030.zip ./restore/

# 手动复制需要的文件回原位置
```

## 📞 技术支持

如果遇到问题，请检查：
1. 清理报告中的错误信息
2. 确认Python版本和依赖
3. 检查文件权限和路径

---

**最后提醒**: 此工具功能强大，请谨慎使用。建议在充分测试和备份后再在生产环境中使用。
