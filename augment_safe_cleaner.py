#!/usr/bin/env python3
"""
VSCode Augment插件安全清理工具（保护聊天记录数据库）
排除关键数据库路径，确保聊天记录导出功能不受影响

使用方法:
    python augment_safe_cleaner.py --dry-run
    python augment_safe_cleaner.py --backup
    python augment_safe_cleaner.py --help
"""

import os
import sys
import shutil
import argparse
import json
import zipfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set

class AugmentSafeCleaner:
    def __init__(self):
        self.user_home = os.path.expanduser("~")
        self.deleted_files = []
        self.deleted_dirs = []
        self.failed_deletions = []
        self.protected_paths = []
        self.total_size_freed = 0
        self.backup_path = None
        
        # 从路径分析.txt文件中读取所有路径
        self.all_paths = self.load_paths_from_file()
        
        # 定义需要保护的关键路径模式
        self.protected_patterns = [
            # 核心数据库路径（绝对不能删除）
            r'workspaceStorage\\.*\\Augment\.vscode-augment\\augment-kv-store',
            # 全局状态数据（可能包含重要配置）
            r'globalStorage\\augment\.vscode-augment',
            # 工作区根目录（包含数据库）
            r'workspaceStorage\\.*\\Augment\.vscode-augment$',
            # 插件安装文件
            r'\.vscode\\extensions\\augment\.vscode-augment-',
            r'\.cursor\\extensions\\augment\.vscode-augment-'
        ]
        
        # 分类所有路径
        self.categorize_safe_paths()
    
    def load_paths_from_file(self) -> List[str]:
        """从路径分析.txt文件加载所有路径"""
        paths_file = "路径分析.txt"
        if not os.path.exists(paths_file):
            print(f"❌ 未找到路径分析文件: {paths_file}")
            return []
        
        try:
            with open(paths_file, 'r', encoding='utf-8') as f:
                paths = [line.strip() for line in f if line.strip()]
            print(f"📁 从 {paths_file} 加载了 {len(paths)} 个路径")
            return paths
        except Exception as e:
            print(f"❌ 读取路径文件失败: {e}")
            return []
    
    def is_protected_path(self, path: str) -> bool:
        """判断路径是否需要保护（不删除）"""
        import re
        
        for pattern in self.protected_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return True
        return False
    
    def should_exclude_path(self, path: str) -> bool:
        """判断是否应该排除此路径（不相关文件）"""
        exclude_patterns = [
            'networkx',
            'torchvision', 
            'zod',
            'Python312\\Lib\\site-packages',
            '__pycache__',
            '.pyc',
            '.pyi',
            'npm-cache',
            'uv\\cache',
            '.langflow-venv'
        ]
        
        path_lower = path.lower()
        return any(pattern.lower() in path_lower for pattern in exclude_patterns)
    
    def categorize_safe_paths(self):
        """将所有路径分类，排除受保护的路径"""
        self.categories = {
            "safe_logs": {
                "description": "日志文件（安全删除）",
                "paths": []
            },
            "safe_cache": {
                "description": "缓存文件（安全删除）",
                "paths": []
            },
            "safe_blackbox": {
                "description": "BlackBox插件中的Augment数据（安全删除）",
                "paths": []
            },
            "safe_system": {
                "description": "系统相关文件（安全删除）",
                "paths": []
            },
            "safe_config": {
                "description": "非关键配置文件（安全删除）",
                "paths": []
            },
            "safe_other": {
                "description": "其他可安全删除的文件",
                "paths": []
            }
        }
        
        protected_count = 0
        
        for path in self.all_paths:
            # 跳过不相关的文件
            if self.should_exclude_path(path):
                continue
            
            # 检查是否为受保护的路径
            if self.is_protected_path(path):
                self.protected_paths.append(path)
                protected_count += 1
                continue
            
            # 分类可安全删除的路径
            category = self.classify_safe_path(path)
            if category:
                self.categories[category]["paths"].append(path)
        
        print(f"🛡️  保护了 {protected_count} 个关键路径")
    
    def classify_safe_path(self, path: str) -> str:
        """对可安全删除的路径进行分类"""
        path_lower = path.lower()
        
        # 日志文件（安全删除）
        if 'logs' in path_lower and 'exthost' in path_lower and 'augment.vscode-augment' in path_lower:
            return "safe_logs"
        
        # 缓存文件（安全删除）
        if 'cachedextensionvsixs' in path_lower and 'augment.vscode-augment' in path_lower:
            return "safe_cache"
        
        # BlackBox数据（安全删除）
        if 'blackboxapp.blackbox' in path_lower and 'augment' in path_lower:
            return "safe_blackbox"
        
        # 系统文件（安全删除）
        if 'recent' in path_lower and '.lnk' in path_lower and 'augment' in path_lower:
            return "safe_system"
        
        # 非关键配置文件（安全删除）
        if 'augment-activation.json' in path_lower or 'augment-modifier-hacker' in path_lower:
            return "safe_config"
        
        # 用户资源文件（但不是核心数据库）
        if 'augment-user-assets' in path_lower:
            return "safe_other"
        
        # IntelliJ数据（安全删除）
        if '.augmentcode' in path and 'intellij' in path_lower:
            return "safe_other"
        
        # 其他Augment相关但非核心的文件
        if 'augment' in path_lower:
            return "safe_other"
        
        return None
    
    def get_file_size(self, path: str) -> int:
        """获取文件或目录大小"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            continue
                return total_size
        except (OSError, FileNotFoundError):
            pass
        return 0
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def preview_safe_deletion(self) -> None:
        """预览安全删除的文件"""
        print("\n" + "="*80)
        print("📋 安全清理预览（保护聊天记录数据库）")
        print("="*80)
        
        # 显示受保护的路径
        if self.protected_paths:
            print(f"\n🛡️  受保护的路径（将被保留）:")
            print("-" * 50)
            protected_existing = [p for p in self.protected_paths if os.path.exists(p)]
            for i, path in enumerate(protected_existing[:10]):
                size = self.get_file_size(path)
                file_type = "📁" if os.path.isdir(path) else "📄"
                print(f"  {file_type} {path} ({self.format_size(size)})")
            
            if len(protected_existing) > 10:
                print(f"  ... 还有 {len(protected_existing) - 10} 个受保护的路径")
            
            protected_size = sum(self.get_file_size(p) for p in protected_existing)
            print(f"  🛡️  保护数据总计: {len(protected_existing)} 项, {self.format_size(protected_size)}")
        
        # 显示将要删除的文件
        total_files = 0
        total_size = 0
        
        for category, info in self.categories.items():
            paths = [p for p in info["paths"] if os.path.exists(p)]
            if not paths:
                continue
                
            print(f"\n📁 {info['description']}:")
            print("-" * 50)
            
            category_size = 0
            category_count = 0
            
            # 显示前5个文件
            for i, path in enumerate(paths[:5]):
                size = self.get_file_size(path)
                file_type = "📁" if os.path.isdir(path) else "📄"
                print(f"  {file_type} {path} ({self.format_size(size)})")
                category_size += size
                category_count += 1
            
            # 如果有更多文件，显示统计
            if len(paths) > 5:
                remaining_size = sum(self.get_file_size(p) for p in paths[5:])
                category_size += remaining_size
                category_count = len(paths)
                print(f"  ... 还有 {len(paths) - 5} 个文件")
            
            print(f"  📊 小计: {category_count} 项, {self.format_size(category_size)}")
            total_files += category_count
            total_size += category_size
        
        print(f"\n🎯 安全删除总计: {total_files} 项, {self.format_size(total_size)}")
        print("="*80)
        print("✅ 聊天记录数据库已受保护，导出功能不会受影响")
        print("✅ 插件安装文件已受保护，Augment功能正常")
        print("="*80)

    def create_backup(self) -> str:
        """创建备份（仅备份将要删除的文件）"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_zip = f"augment_safe_backup_{timestamp}.zip"

        print(f"🔄 正在创建安全备份到 {backup_zip}...")

        try:
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                backup_count = 0

                for category, info in self.categories.items():
                    for path in info["paths"]:
                        if os.path.exists(path):
                            try:
                                if os.path.isfile(path):
                                    # 备份文件
                                    arcname = os.path.join(category, os.path.relpath(path, self.user_home))
                                    zipf.write(path, arcname)
                                    backup_count += 1
                                elif os.path.isdir(path):
                                    # 备份目录
                                    for root, dirs, files in os.walk(path):
                                        for file in files:
                                            file_path = os.path.join(root, file)
                                            arcname = os.path.join(category, os.path.relpath(file_path, self.user_home))
                                            try:
                                                zipf.write(file_path, arcname)
                                                backup_count += 1
                                            except Exception as e:
                                                print(f"⚠️  备份文件失败 {file_path}: {e}")
                            except Exception as e:
                                print(f"⚠️  备份项目失败 {path}: {e}")

            backup_size = os.path.getsize(backup_zip)
            print(f"✅ 安全备份完成: {backup_count} 个文件, 大小: {self.format_size(backup_size)}")
            return backup_zip

        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None

    def delete_file_or_dir(self, path: str) -> tuple:
        """删除文件或目录"""
        if not os.path.exists(path):
            return False, 0

        size = self.get_file_size(path)

        try:
            if os.path.isfile(path):
                os.remove(path)
                self.deleted_files.append(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
                self.deleted_dirs.append(path)
            return True, size
        except Exception as e:
            self.failed_deletions.append((path, str(e)))
            return False, 0

    def perform_safe_deletion(self, dry_run: bool = False) -> None:
        """执行安全删除操作"""
        if dry_run:
            self.preview_safe_deletion()
            return

        print("\n" + "="*80)
        print("🗑️  开始安全清理操作")
        print("="*80)

        for category, info in self.categories.items():
            existing_paths = [p for p in info["paths"] if os.path.exists(p)]
            if not existing_paths:
                continue

            print(f"\n🔄 正在清理: {info['description']}")

            for path in existing_paths:
                success, size = self.delete_file_or_dir(path)
                if success:
                    file_type = "📁" if os.path.isdir(path) else "📄"
                    print(f"  ✅ 已删除 {file_type} {os.path.basename(path)} ({self.format_size(size)})")
                    self.total_size_freed += size
                else:
                    print(f"  ❌ 删除失败: {os.path.basename(path)}")

    def check_processes(self) -> bool:
        """检查VSCode和Cursor是否正在运行"""
        import subprocess
        try:
            result = subprocess.run(['tasklist', '/fo', 'csv'], capture_output=True, text=True)
            if result.returncode == 0:
                processes = result.stdout.lower()
                if 'code.exe' in processes or 'cursor.exe' in processes:
                    return True
        except Exception:
            pass
        return False

    def generate_report(self, dry_run: bool, backup_created: bool) -> str:
        """生成清理报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"augment_safe_cleanup_report_{timestamp}.json"

        report = {
            "cleanup_time": datetime.now().isoformat(),
            "mode": "safe_cleanup_preserve_chat_db",
            "dry_run": dry_run,
            "backup_created": backup_created,
            "backup_path": self.backup_path,
            "total_paths_analyzed": len(self.all_paths),
            "protected_paths_count": len(self.protected_paths),
            "categories": {cat: len(info["paths"]) for cat, info in self.categories.items()},
            "statistics": {
                "deleted_files": len(self.deleted_files),
                "deleted_directories": len(self.deleted_dirs),
                "failed_deletions": len(self.failed_deletions),
                "total_size_freed": self.total_size_freed,
                "total_size_freed_human": self.format_size(self.total_size_freed)
            },
            "protected_paths": self.protected_paths,
            "deleted_files": self.deleted_files,
            "deleted_directories": self.deleted_dirs,
            "failed_deletions": self.failed_deletions
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"\n📄 安全清理报告已保存: {report_file}")
        return report_file

    def clean(self, dry_run: bool = False, create_backup: bool = False) -> None:
        """主清理函数"""
        print("🧹 VSCode Augment插件安全清理工具")
        print("="*80)
        print("🛡️  目标: 安全清理，保护聊天记录数据库和插件功能")
        print("="*80)

        if not self.all_paths:
            print("❌ 未找到路径分析文件或文件为空")
            return

        # 统计信息
        total_safe_paths = sum(len(info["paths"]) for info in self.categories.values())
        existing_safe_paths = sum(len([p for p in info["paths"] if os.path.exists(p)])
                                for info in self.categories.values())
        existing_protected = len([p for p in self.protected_paths if os.path.exists(p)])

        print(f"📊 分析了 {len(self.all_paths)} 个路径")
        print(f"🛡️  保护了 {existing_protected} 个关键路径（聊天记录数据库等）")
        print(f"🗑️  发现 {existing_safe_paths} 个可安全删除的项目")

        if existing_safe_paths == 0:
            print("✅ 未找到可安全删除的文件")
            return

        # 安全检查
        if not dry_run:
            if self.check_processes():
                print("\n⚠️  警告: 检测到VSCode或Cursor正在运行")
                print("   请关闭所有VSCode和Cursor窗口后再运行此脚本")
                response = input("是否继续? (y/N): ").strip().lower()
                if response != 'y':
                    print("❌ 操作已取消")
                    return

        # 创建备份
        backup_created = False
        if create_backup and not dry_run:
            self.backup_path = self.create_backup()
            backup_created = self.backup_path is not None

        # 执行删除
        if not dry_run:
            print(f"\n⚠️  警告: 即将安全删除 {existing_safe_paths} 个项目!")
            print("   以下内容将被保护（不会删除）:")
            print("   ✅ 聊天记录数据库 (augment-kv-store)")
            print("   ✅ 全局状态数据 (globalStorage)")
            print("   ✅ 工作区核心数据")
            print("   ✅ 插件安装文件")
            print()
            print("   此操作不可逆转，但聊天记录导出功能不会受影响")
            response = input("确认继续安全删除? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 操作已取消")
                return

        self.perform_safe_deletion(dry_run)

        # 显示结果
        if not dry_run:
            print("\n" + "="*80)
            print("📊 安全清理统计")
            print("="*80)
            print(f"✅ 已删除文件: {len(self.deleted_files)} 个")
            print(f"✅ 已删除目录: {len(self.deleted_dirs)} 个")
            print(f"❌ 删除失败: {len(self.failed_deletions)} 个")
            print(f"💾 释放空间: {self.format_size(self.total_size_freed)}")
            print(f"🛡️  保护路径: {existing_protected} 个")

            if self.failed_deletions:
                print("\n❌ 删除失败的项目:")
                for path, error in self.failed_deletions:
                    print(f"  - {os.path.basename(path)}: {error}")

            print("\n🎉 安全清理完成！")
            print("✅ 聊天记录数据库已保护，导出功能正常")
            print("✅ Augment插件功能完全保留")

        # 生成报告
        self.generate_report(dry_run, backup_created)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="VSCode Augment插件安全清理工具（保护聊天记录数据库）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预览安全清理内容
  python augment_safe_cleaner.py --dry-run

  # 执行安全清理并创建备份
  python augment_safe_cleaner.py --backup

  # 执行安全清理（不备份）
  python augment_safe_cleaner.py

安全保护:
  - 保护聊天记录数据库 (augment-kv-store)
  - 保护全局状态数据 (globalStorage)
  - 保护工作区核心数据
  - 保护插件安装文件
  - 确保导出功能不受影响

安全删除:
  - 日志文件（不影响功能）
  - 缓存文件（可重新生成）
  - BlackBox插件数据
  - 系统快捷方式
  - 非关键配置文件
        """
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，仅显示将要删除的文件，不实际删除'
    )

    parser.add_argument(
        '--backup',
        action='store_true',
        help='在删除前创建备份文件'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='VSCode Augment Safe Cleaner v1.0.0'
    )

    args = parser.parse_args()

    # 安全警告
    if not args.dry_run:
        print("⚠️  安全警告:")
        print("   此工具将安全清理VSCode Augment插件数据")
        print("   聊天记录数据库和插件功能将被完全保护")
        print("   仅删除日志、缓存等不影响功能的文件")
        print("   删除操作不可逆转，请确保已了解清理范围")
        print()

        response = input("您已阅读并理解上述说明，是否继续? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 操作已取消")
            sys.exit(0)

    # 执行清理
    try:
        cleaner = AugmentSafeCleaner()
        cleaner.clean(args.dry_run, args.backup)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
