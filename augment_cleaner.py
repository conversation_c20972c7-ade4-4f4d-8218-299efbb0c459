#!/usr/bin/env python3
"""
VSCode Augment插件安全清理脚本
基于全面调研报告，提供安全的清理选项

使用方法:
    python augment_cleaner.py --mode user-data --dry-run
    python augment_cleaner.py --mode complete --backup
    python augment_cleaner.py --help

安全警告:
    - 此脚本将删除VSCode Augment插件相关文件
    - 建议在执行前使用 --dry-run 预览将要删除的文件
    - 重要数据请提前备份
    - 确保VSCode和Cursor已完全关闭
"""

import os
import sys
import shutil
import argparse
import json
import glob
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple
import zipfile

class AugmentCleaner:
    def __init__(self):
        self.user_home = os.path.expanduser("~")
        self.deleted_files = []
        self.deleted_dirs = []
        self.failed_deletions = []
        self.total_size_freed = 0
        self.backup_path = None
        
        # 基于调研报告的文件分类
        self.file_categories = {
            "user_data": {
                "description": "用户数据文件（对话记录、工作区数据等）",
                "paths": [
                    # VSCode全局存储
                    os.path.join(self.user_home, "AppData", "Roaming", "Code", "User", "globalStorage", "augment.vscode-augment"),
                    # VSCode工作区存储（通配符模式）
                    os.path.join(self.user_home, "AppData", "Roaming", "Code", "User", "workspaceStorage", "*", "Augment.vscode-augment"),
                    # Cursor工作区存储
                    os.path.join(self.user_home, "AppData", "Roaming", "Cursor", "User", "workspaceStorage", "*", "Augment.vscode-augment"),
                    # Augment专用目录
                    os.path.join(self.user_home, ".augment"),
                    os.path.join(self.user_home, ".augmentcode"),
                ]
            },
            "logs": {
                "description": "日志文件",
                "paths": [
                    # VSCode日志
                    os.path.join(self.user_home, "AppData", "Roaming", "Code", "logs", "*", "window*", "exthost", "Augment.vscode-augment"),
                    # Cursor日志
                    os.path.join(self.user_home, "AppData", "Roaming", "Cursor", "logs", "*", "window*", "exthost", "Augment.vscode-augment"),
                ]
            },
            "cache": {
                "description": "缓存文件",
                "paths": [
                    # VSCode扩展缓存
                    os.path.join(self.user_home, "AppData", "Roaming", "Code", "CachedExtensionVSIXs", "augment.vscode-augment-*"),
                ]
            },
            "config": {
                "description": "配置文件",
                "paths": [
                    # Augment激活配置
                    os.path.join(self.user_home, "AppData", "Roaming", "augment-modifier-hacker", "augment-activation.json"),
                ]
            },
            "extensions": {
                "description": "插件安装文件",
                "paths": [
                    # VSCode扩展
                    os.path.join(self.user_home, ".vscode", "extensions", "augment.vscode-augment-*"),
                    # Cursor扩展
                    os.path.join(self.user_home, ".cursor", "extensions", "augment.vscode-augment-*"),
                ]
            },
            "system": {
                "description": "系统相关文件",
                "paths": [
                    # Windows最近访问记录
                    os.path.join(self.user_home, "AppData", "Roaming", "Microsoft", "Windows", "Recent", "*augment*.lnk"),
                ]
            }
        }
    
    def get_file_size(self, path: str) -> int:
        """获取文件或目录大小"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            continue
                return total_size
        except (OSError, FileNotFoundError):
            pass
        return 0
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def expand_paths(self, patterns: List[str]) -> List[str]:
        """展开通配符路径"""
        expanded_paths = []
        for pattern in patterns:
            if '*' in pattern:
                matches = glob.glob(pattern)
                expanded_paths.extend(matches)
            else:
                if os.path.exists(pattern):
                    expanded_paths.append(pattern)
        return expanded_paths
    
    def scan_files(self, mode: str) -> Dict[str, List[str]]:
        """扫描要删除的文件"""
        files_to_delete = {}
        
        if mode == "user-data":
            categories = ["user_data", "logs", "cache", "config", "system"]
        elif mode == "complete":
            categories = list(self.file_categories.keys())
        else:
            raise ValueError(f"未知的清理模式: {mode}")
        
        for category in categories:
            if category in self.file_categories:
                paths = self.expand_paths(self.file_categories[category]["paths"])
                files_to_delete[category] = paths
        
        return files_to_delete
    
    def create_backup(self, files_to_delete: Dict[str, List[str]]) -> str:
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = f"augment_backup_{timestamp}"
        backup_zip = f"{backup_dir}.zip"
        
        print(f"🔄 正在创建备份到 {backup_zip}...")
        
        try:
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                backup_count = 0
                for category, paths in files_to_delete.items():
                    for path in paths:
                        if os.path.exists(path):
                            if os.path.isfile(path):
                                # 备份文件
                                arcname = os.path.join(category, os.path.relpath(path, self.user_home))
                                zipf.write(path, arcname)
                                backup_count += 1
                            elif os.path.isdir(path):
                                # 备份目录
                                for root, dirs, files in os.walk(path):
                                    for file in files:
                                        file_path = os.path.join(root, file)
                                        arcname = os.path.join(category, os.path.relpath(file_path, self.user_home))
                                        try:
                                            zipf.write(file_path, arcname)
                                            backup_count += 1
                                        except Exception as e:
                                            print(f"⚠️  备份文件失败 {file_path}: {e}")
            
            backup_size = os.path.getsize(backup_zip)
            print(f"✅ 备份完成: {backup_count} 个文件, 大小: {self.format_size(backup_size)}")
            return backup_zip
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def delete_file_or_dir(self, path: str, dry_run: bool = False) -> Tuple[bool, int]:
        """删除文件或目录"""
        if not os.path.exists(path):
            return False, 0
        
        size = self.get_file_size(path)
        
        if dry_run:
            return True, size
        
        try:
            if os.path.isfile(path):
                os.remove(path)
                self.deleted_files.append(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
                self.deleted_dirs.append(path)
            return True, size
        except Exception as e:
            self.failed_deletions.append((path, str(e)))
            return False, 0
    
    def preview_deletion(self, files_to_delete: Dict[str, List[str]]) -> None:
        """预览将要删除的文件"""
        print("\n" + "="*60)
        print("📋 删除预览 (--dry-run 模式)")
        print("="*60)
        
        total_files = 0
        total_size = 0
        
        for category, paths in files_to_delete.items():
            if not paths:
                continue
                
            category_info = self.file_categories[category]
            print(f"\n📁 {category_info['description']}:")
            print("-" * 40)
            
            category_size = 0
            category_count = 0
            
            for path in paths:
                if os.path.exists(path):
                    size = self.get_file_size(path)
                    file_type = "📁" if os.path.isdir(path) else "📄"
                    print(f"  {file_type} {path} ({self.format_size(size)})")
                    category_size += size
                    category_count += 1
            
            if category_count > 0:
                print(f"  小计: {category_count} 项, {self.format_size(category_size)}")
                total_files += category_count
                total_size += category_size
        
        print(f"\n📊 总计: {total_files} 项, {self.format_size(total_size)}")
        print("="*60)
    
    def perform_deletion(self, files_to_delete: Dict[str, List[str]], dry_run: bool = False) -> None:
        """执行删除操作"""
        if dry_run:
            self.preview_deletion(files_to_delete)
            return
        
        print("\n" + "="*60)
        print("🗑️  开始删除操作")
        print("="*60)
        
        for category, paths in files_to_delete.items():
            if not paths:
                continue
                
            category_info = self.file_categories[category]
            print(f"\n🔄 正在清理: {category_info['description']}")
            
            for path in paths:
                if os.path.exists(path):
                    success, size = self.delete_file_or_dir(path, dry_run)
                    if success:
                        file_type = "📁" if os.path.isdir(path) else "📄"
                        print(f"  ✅ 已删除 {file_type} {path} ({self.format_size(size)})")
                        self.total_size_freed += size
                    else:
                        print(f"  ❌ 删除失败: {path}")
    
    def generate_report(self, mode: str, dry_run: bool, backup_created: bool) -> None:
        """生成清理报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"augment_cleanup_report_{timestamp}.json"
        
        report = {
            "cleanup_time": datetime.now().isoformat(),
            "mode": mode,
            "dry_run": dry_run,
            "backup_created": backup_created,
            "backup_path": self.backup_path,
            "statistics": {
                "deleted_files": len(self.deleted_files),
                "deleted_directories": len(self.deleted_dirs),
                "failed_deletions": len(self.failed_deletions),
                "total_size_freed": self.total_size_freed,
                "total_size_freed_human": self.format_size(self.total_size_freed)
            },
            "deleted_files": self.deleted_files,
            "deleted_directories": self.deleted_dirs,
            "failed_deletions": self.failed_deletions
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 清理报告已保存: {report_file}")
        return report_file

    def check_processes(self) -> bool:
        """检查VSCode和Cursor是否正在运行"""
        import subprocess
        try:
            # 检查Windows进程
            result = subprocess.run(['tasklist', '/fo', 'csv'], capture_output=True, text=True)
            if result.returncode == 0:
                processes = result.stdout.lower()
                if 'code.exe' in processes or 'cursor.exe' in processes:
                    return True
        except Exception:
            pass
        return False

    def clean(self, mode: str, dry_run: bool = False, create_backup: bool = False) -> None:
        """主清理函数"""
        print("🧹 VSCode Augment插件清理工具")
        print("="*60)

        # 安全检查
        if not dry_run:
            if self.check_processes():
                print("⚠️  警告: 检测到VSCode或Cursor正在运行")
                print("   请关闭所有VSCode和Cursor窗口后再运行此脚本")
                response = input("是否继续? (y/N): ").strip().lower()
                if response != 'y':
                    print("❌ 操作已取消")
                    return

        # 扫描文件
        print(f"🔍 扫描模式: {mode}")
        files_to_delete = self.scan_files(mode)

        # 统计信息
        total_items = sum(len(paths) for paths in files_to_delete.values())
        if total_items == 0:
            print("✅ 未找到需要清理的文件")
            return

        print(f"📊 发现 {total_items} 个项目需要处理")

        # 创建备份
        backup_created = False
        if create_backup and not dry_run:
            self.backup_path = self.create_backup(files_to_delete)
            backup_created = self.backup_path is not None

        # 执行删除
        if not dry_run:
            print("\n⚠️  警告: 即将开始删除操作!")
            print("   此操作不可逆转，请确认您已经备份了重要数据")
            response = input("确认继续删除? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 操作已取消")
                return

        self.perform_deletion(files_to_delete, dry_run)

        # 显示结果
        if not dry_run:
            print("\n" + "="*60)
            print("📊 清理完成统计")
            print("="*60)
            print(f"✅ 已删除文件: {len(self.deleted_files)} 个")
            print(f"✅ 已删除目录: {len(self.deleted_dirs)} 个")
            print(f"❌ 删除失败: {len(self.failed_deletions)} 个")
            print(f"💾 释放空间: {self.format_size(self.total_size_freed)}")

            if self.failed_deletions:
                print("\n❌ 删除失败的项目:")
                for path, error in self.failed_deletions:
                    print(f"  - {path}: {error}")

        # 生成报告
        self.generate_report(mode, dry_run, backup_created)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="VSCode Augment插件安全清理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预览用户数据清理
  python augment_cleaner.py --mode user-data --dry-run

  # 清理用户数据并创建备份
  python augment_cleaner.py --mode user-data --backup

  # 完全清理（包括插件本身）
  python augment_cleaner.py --mode complete --backup

清理模式说明:
  user-data: 仅清理用户数据，保留插件安装文件
  complete:  完全清理，包括插件本身和所有数据

安全提示:
  - 建议先使用 --dry-run 预览要删除的文件
  - 重要数据请使用 --backup 创建备份
  - 确保VSCode和Cursor已完全关闭
        """
    )

    parser.add_argument(
        '--mode',
        choices=['user-data', 'complete'],
        required=True,
        help='清理模式: user-data(仅用户数据) 或 complete(完全清理)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，仅显示将要删除的文件，不实际删除'
    )

    parser.add_argument(
        '--backup',
        action='store_true',
        help='在删除前创建备份文件'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='VSCode Augment Cleaner v1.0.0'
    )

    args = parser.parse_args()

    # 安全警告
    if not args.dry_run:
        print("⚠️  安全警告:")
        print("   此工具将删除VSCode Augment插件相关文件")
        print("   删除操作不可逆转，请确保已备份重要数据")
        print("   建议先使用 --dry-run 参数预览要删除的文件")
        print()

        response = input("您已阅读并理解上述警告，是否继续? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 操作已取消")
            sys.exit(0)

    # 执行清理
    try:
        cleaner = AugmentCleaner()
        cleaner.clean(args.mode, args.dry_run, args.backup)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
