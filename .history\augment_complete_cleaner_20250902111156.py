#!/usr/bin/env python3
"""
VSCode Augment插件完全清理工具
基于路径分析.txt文件，确保删除所有相关文件（除插件本身外）

使用方法:
    python augment_complete_cleaner.py --dry-run
    python augment_complete_cleaner.py --backup
    python augment_complete_cleaner.py --help
"""

import os
import sys
import shutil
import argparse
import json
import zipfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set

class AugmentCompleteCleaner:
    def __init__(self):
        self.user_home = os.path.expanduser("~")
        self.deleted_files = []
        self.deleted_dirs = []
        self.failed_deletions = []
        self.total_size_freed = 0
        self.backup_path = None
        
        # 从路径分析.txt文件中读取所有路径
        self.all_paths = self.load_paths_from_file()
        
        # 分类所有路径（排除插件安装文件）
        self.categorize_all_paths()
    
    def load_paths_from_file(self) -> List[str]:
        """从路径分析.txt文件加载所有路径"""
        paths_file = "路径分析.txt"
        if not os.path.exists(paths_file):
            print(f"❌ 未找到路径分析文件: {paths_file}")
            return []
        
        try:
            with open(paths_file, 'r', encoding='utf-8') as f:
                paths = [line.strip() for line in f if line.strip()]
            print(f"📁 从 {paths_file} 加载了 {len(paths)} 个路径")
            return paths
        except Exception as e:
            print(f"❌ 读取路径文件失败: {e}")
            return []
    
    def categorize_all_paths(self):
        """将所有路径分类，排除插件安装文件"""
        self.categories = {
            "user_data": {
                "description": "用户数据文件（对话记录、工作区数据等）",
                "paths": []
            },
            "logs": {
                "description": "日志文件",
                "paths": []
            },
            "cache": {
                "description": "缓存文件",
                "paths": []
            },
            "config": {
                "description": "配置文件",
                "paths": []
            },
            "blackbox_data": {
                "description": "BlackBox插件中的Augment数据",
                "paths": []
            },
            "intellij_data": {
                "description": "IntelliJ中的Augment数据",
                "paths": []
            },
            "system": {
                "description": "系统相关文件",
                "paths": []
            },
            "other": {
                "description": "其他相关文件",
                "paths": []
            }
        }
        
        # 插件安装文件（需要保留的）
        plugin_patterns = [
            r'\.vscode\\extensions\\augment\.vscode-augment-',
            r'\.cursor\\extensions\\augment\.vscode-augment-',
        ]
        
        for path in self.all_paths:
            # 跳过插件安装文件
            if any(pattern in path for pattern in plugin_patterns):
                continue
            
            # 跳过不相关的文件（NetworkX等）
            if self.should_exclude_path(path):
                continue
            
            # 分类路径
            category = self.classify_path(path)
            self.categories[category]["paths"].append(path)
    
    def should_exclude_path(self, path: str) -> bool:
        """判断是否应该排除此路径"""
        exclude_patterns = [
            'networkx',
            'torchvision',
            'zod',
            'Python312\\Lib\\site-packages',
            '__pycache__',
            '.pyc',
            '.pyi',
            'npm-cache',
            'uv\\cache',
            '.langflow-venv'
        ]
        
        path_lower = path.lower()
        return any(pattern.lower() in path_lower for pattern in exclude_patterns)
    
    def classify_path(self, path: str) -> str:
        """对路径进行分类"""
        path_lower = path.lower()
        
        # 用户数据文件
        if ('workspacestorage' in path_lower and 'augment.vscode-augment' in path_lower) or \
           ('globalstorage' in path_lower and 'augment.vscode-augment' in path_lower) or \
           path_lower.endswith('.augment') or path_lower.endswith('.augmentcode'):
            return "user_data"
        
        # 日志文件
        if 'logs' in path_lower and ('exthost' in path_lower or 'augment.log' in path_lower):
            return "logs"
        
        # 缓存文件
        if 'cachedextensionvsixs' in path_lower or 'cache' in path_lower:
            return "cache"
        
        # BlackBox数据
        if 'blackboxapp.blackbox' in path_lower and 'augment' in path_lower:
            return "blackbox_data"
        
        # IntelliJ数据
        if '.augmentcode' in path and 'intellij' in path_lower:
            return "intellij_data"
        
        # 配置文件
        if 'augment-activation.json' in path_lower or \
           'augment-modifier-hacker' in path_lower:
            return "config"
        
        # 系统文件
        if 'recent' in path_lower and '.lnk' in path_lower:
            return "system"
        
        # 其他
        return "other"
    
    def get_file_size(self, path: str) -> int:
        """获取文件或目录大小"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            continue
                return total_size
        except (OSError, FileNotFoundError):
            pass
        return 0
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def preview_deletion(self) -> None:
        """预览将要删除的文件"""
        print("\n" + "="*80)
        print("📋 完全清理预览 (基于路径分析.txt)")
        print("="*80)
        
        total_files = 0
        total_size = 0
        
        for category, info in self.categories.items():
            paths = [p for p in info["paths"] if os.path.exists(p)]
            if not paths:
                continue
                
            print(f"\n📁 {info['description']}:")
            print("-" * 50)
            
            category_size = 0
            category_count = 0
            
            # 显示前10个文件
            for i, path in enumerate(paths[:10]):
                size = self.get_file_size(path)
                file_type = "📁" if os.path.isdir(path) else "📄"
                print(f"  {file_type} {path} ({self.format_size(size)})")
                category_size += size
                category_count += 1
            
            # 如果有更多文件，显示统计
            if len(paths) > 10:
                remaining_size = sum(self.get_file_size(p) for p in paths[10:])
                category_size += remaining_size
                category_count = len(paths)
                print(f"  ... 还有 {len(paths) - 10} 个文件")
            
            print(f"  📊 小计: {category_count} 项, {self.format_size(category_size)}")
            total_files += category_count
            total_size += category_size
        
        print(f"\n🎯 总计: {total_files} 项, {self.format_size(total_size)}")
        print("="*80)
        print("⚠️  注意: 插件安装文件将被保留")
        print("="*80)
    
    def create_backup(self) -> str:
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_zip = f"augment_complete_backup_{timestamp}.zip"
        
        print(f"🔄 正在创建完整备份到 {backup_zip}...")
        
        try:
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                backup_count = 0
                
                for category, info in self.categories.items():
                    for path in info["paths"]:
                        if os.path.exists(path):
                            try:
                                if os.path.isfile(path):
                                    # 备份文件
                                    arcname = os.path.join(category, os.path.relpath(path, self.user_home))
                                    zipf.write(path, arcname)
                                    backup_count += 1
                                elif os.path.isdir(path):
                                    # 备份目录
                                    for root, dirs, files in os.walk(path):
                                        for file in files:
                                            file_path = os.path.join(root, file)
                                            arcname = os.path.join(category, os.path.relpath(file_path, self.user_home))
                                            try:
                                                zipf.write(file_path, arcname)
                                                backup_count += 1
                                            except Exception as e:
                                                print(f"⚠️  备份文件失败 {file_path}: {e}")
                            except Exception as e:
                                print(f"⚠️  备份项目失败 {path}: {e}")
            
            backup_size = os.path.getsize(backup_zip)
            print(f"✅ 备份完成: {backup_count} 个文件, 大小: {self.format_size(backup_size)}")
            return backup_zip
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def delete_file_or_dir(self, path: str) -> tuple:
        """删除文件或目录"""
        if not os.path.exists(path):
            return False, 0
        
        size = self.get_file_size(path)
        
        try:
            if os.path.isfile(path):
                os.remove(path)
                self.deleted_files.append(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
                self.deleted_dirs.append(path)
            return True, size
        except Exception as e:
            self.failed_deletions.append((path, str(e)))
            return False, 0
    
    def perform_deletion(self, dry_run: bool = False) -> None:
        """执行删除操作"""
        if dry_run:
            self.preview_deletion()
            return
        
        print("\n" + "="*80)
        print("🗑️  开始完全清理操作")
        print("="*80)
        
        for category, info in self.categories.items():
            existing_paths = [p for p in info["paths"] if os.path.exists(p)]
            if not existing_paths:
                continue
                
            print(f"\n🔄 正在清理: {info['description']}")
            
            for path in existing_paths:
                success, size = self.delete_file_or_dir(path)
                if success:
                    file_type = "📁" if os.path.isdir(path) else "📄"
                    print(f"  ✅ 已删除 {file_type} {os.path.basename(path)} ({self.format_size(size)})")
                    self.total_size_freed += size
                else:
                    print(f"  ❌ 删除失败: {os.path.basename(path)}")
    
    def generate_report(self, dry_run: bool, backup_created: bool) -> str:
        """生成清理报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"augment_complete_cleanup_report_{timestamp}.json"
        
        report = {
            "cleanup_time": datetime.now().isoformat(),
            "mode": "complete_data_cleanup",
            "dry_run": dry_run,
            "backup_created": backup_created,
            "backup_path": self.backup_path,
            "total_paths_analyzed": len(self.all_paths),
            "categories": {cat: len(info["paths"]) for cat, info in self.categories.items()},
            "statistics": {
                "deleted_files": len(self.deleted_files),
                "deleted_directories": len(self.deleted_dirs),
                "failed_deletions": len(self.failed_deletions),
                "total_size_freed": self.total_size_freed,
                "total_size_freed_human": self.format_size(self.total_size_freed)
            },
            "deleted_files": self.deleted_files,
            "deleted_directories": self.deleted_dirs,
            "failed_deletions": self.failed_deletions
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 完整清理报告已保存: {report_file}")
        return report_file

    def check_processes(self) -> bool:
        """检查VSCode和Cursor是否正在运行"""
        import subprocess
        try:
            result = subprocess.run(['tasklist', '/fo', 'csv'], capture_output=True, text=True)
            if result.returncode == 0:
                processes = result.stdout.lower()
                if 'code.exe' in processes or 'cursor.exe' in processes:
                    return True
        except Exception:
            pass
        return False

    def clean(self, dry_run: bool = False, create_backup: bool = False) -> None:
        """主清理函数"""
        print("🧹 VSCode Augment插件完全清理工具")
        print("="*80)
        print("🎯 目标: 删除所有Augment数据，保留插件安装文件")
        print("="*80)

        if not self.all_paths:
            print("❌ 未找到路径分析文件或文件为空")
            return

        # 统计信息
        total_paths = sum(len(info["paths"]) for info in self.categories.values())
        existing_paths = sum(len([p for p in info["paths"] if os.path.exists(p)])
                           for info in self.categories.values())

        print(f"📊 分析了 {len(self.all_paths)} 个路径")
        print(f"📊 发现 {total_paths} 个相关项目")
        print(f"📊 其中 {existing_paths} 个当前存在")

        if existing_paths == 0:
            print("✅ 未找到需要清理的文件")
            return

        # 安全检查
        if not dry_run:
            if self.check_processes():
                print("\n⚠️  警告: 检测到VSCode或Cursor正在运行")
                print("   请关闭所有VSCode和Cursor窗口后再运行此脚本")
                response = input("是否继续? (y/N): ").strip().lower()
                if response != 'y':
                    print("❌ 操作已取消")
                    return

        # 创建备份
        backup_created = False
        if create_backup and not dry_run:
            self.backup_path = self.create_backup()
            backup_created = self.backup_path is not None

        # 执行删除
        if not dry_run:
            print(f"\n⚠️  警告: 即将删除 {existing_paths} 个Augment相关项目!")
            print("   此操作不可逆转，请确认您已经备份了重要数据")
            print("   插件安装文件将被保留，可以继续使用Augment")
            response = input("确认继续删除? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 操作已取消")
                return

        self.perform_deletion(dry_run)

        # 显示结果
        if not dry_run:
            print("\n" + "="*80)
            print("📊 完全清理统计")
            print("="*80)
            print(f"✅ 已删除文件: {len(self.deleted_files)} 个")
            print(f"✅ 已删除目录: {len(self.deleted_dirs)} 个")
            print(f"❌ 删除失败: {len(self.failed_deletions)} 个")
            print(f"💾 释放空间: {self.format_size(self.total_size_freed)}")

            if self.failed_deletions:
                print("\n❌ 删除失败的项目:")
                for path, error in self.failed_deletions:
                    print(f"  - {os.path.basename(path)}: {error}")

            print("\n🎉 清理完成！插件安装文件已保留，Augment可以继续使用。")

        # 生成报告
        self.generate_report(dry_run, backup_created)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="VSCode Augment插件完全清理工具（基于路径分析.txt）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预览将要删除的所有文件
  python augment_complete_cleaner.py --dry-run

  # 执行完全清理并创建备份
  python augment_complete_cleaner.py --backup

  # 执行完全清理（不备份）
  python augment_complete_cleaner.py

功能说明:
  - 基于路径分析.txt文件，确保覆盖所有发现的Augment相关文件
  - 保留插件安装文件，删除所有用户数据、日志、缓存等
  - 包括BlackBox和IntelliJ中的Augment数据
  - 支持完整备份和详细报告

安全提示:
  - 建议先使用 --dry-run 预览要删除的文件
  - 重要数据请使用 --backup 创建备份
  - 确保VSCode和Cursor已完全关闭
        """
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，仅显示将要删除的文件，不实际删除'
    )

    parser.add_argument(
        '--backup',
        action='store_true',
        help='在删除前创建完整备份文件'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='VSCode Augment Complete Cleaner v1.0.0'
    )

    args = parser.parse_args()

    # 安全警告
    if not args.dry_run:
        print("⚠️  安全警告:")
        print("   此工具将删除所有VSCode Augment插件相关数据")
        print("   包括对话记录、用户设置、缓存、日志等")
        print("   插件安装文件将被保留，可以继续使用Augment")
        print("   删除操作不可逆转，请确保已备份重要数据")
        print()

        response = input("您已阅读并理解上述警告，是否继续? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 操作已取消")
            sys.exit(0)

    # 执行清理
    try:
        cleaner = AugmentCompleteCleaner()
        cleaner.clean(args.dry_run, args.backup)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
